<!doctype html>
<html lang="en">
  <head>
    <link rel="stylesheet" href="/cesium/Widgets/widgets.css">
    <script src="/cesium/Cesium.js"></script>

    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + React + TS</title>
    <style>
      .cesium-viewer-cesiumWidgetContainer {
        background-color: transparent !important;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-BaiuyN7-.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-BL9Dw2LK.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
