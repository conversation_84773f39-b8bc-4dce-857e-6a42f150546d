import React, { useCallback, useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Header } from '../../components/layout/Header';
import { CesiumViewer } from './components/CesiumViewer';
import { CesiumController } from './controllers/CesiumController';
import { TimeController } from './components/TimeController';
import { SideButtons } from './components/SideButtons';
import { LayersPanel } from './components/LayersPanel';
import { SatelliteInfoPanel } from './components/SatelliteInfoPanel';
import { SatelliteBasicInfoPanel, SatelliteApiData } from './components/SatelliteBasicInfoPanel';
import { DebrisInfoPanel } from './components/DebrisInfoPanel';
import { LaunchSiteInfoPanel } from './components/LaunchSiteInfoPanel';
import { LaunchSiteWikiPanel } from './components/LaunchSiteWikiPanel';
import { Satellite } from '../../types/satellite';
import type { SpaceDebris } from '../../types/debris';
import { LaunchSite, WikiLaunchSiteInfo } from '../../types/launchSite';
import { useSearchParams } from 'react-router-dom';
import { apiService } from '../../services/apiService';
import { Modal, message } from 'antd';
import { constellationDataManager } from '../../services/constellationDataManager';

// 定义API响应的接口
interface ApiResponse<T> {
  success?: boolean;
  data?: T;
  results?: T[];
  total?: number;
  page?: number;
  limit?: number;
}

const PageContainer = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #000;
  position: relative;
  overflow: hidden;
`;

const MainContent = styled.div`
  flex: 1;
  position: relative;
  
  .cesium-container {
    width: 100%;
    height: 100%;
  }
`;

export function SpaceSituationPage() {
  const [layersPanelVisible, setLayersPanelVisible] = useState(false);
  const [cesiumController, setCesiumController] = useState<CesiumController | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isPlaying, setIsPlaying] = useState(true);
  const [multiplier, setMultiplier] = useState(1);
  const [currentTime, setCurrentTime] = useState(0);
  const [selectedSatellite, setSelectedSatellite] = useState<Satellite | null>(null);
  const [selectedDebris, setSelectedDebris] = useState<SpaceDebris | null>(null);
  const [selectedLaunchSite, setSelectedLaunchSite] = useState<LaunchSite | null>(null);
  const [isTimelineDisabled, setIsTimelineDisabled] = useState(false);
  const [searchParams] = useSearchParams();

  // 发射场Wiki面板状态
  const [launchSiteWikiInfo, setLaunchSiteWikiInfo] = useState<{siteName: string, wikiInfo: WikiLaunchSiteInfo} | null>(null);
  const [launchSiteWikiVisible, setLaunchSiteWikiVisible] = useState(false);

  // 🌟 新增：卫星基础信息面板状态
  const [satelliteBasicInfo, setSatelliteBasicInfo] = useState<SatelliteApiData | null>(null);
  const [satelliteBasicInfoLoading, setSatelliteBasicInfoLoading] = useState(false);
  const [satelliteBasicInfoError, setSatelliteBasicInfoError] = useState<string | null>(null);
  const [satelliteBasicInfoVisible, setSatelliteBasicInfoVisible] = useState(false);
  const [currentRequestedSatelliteId, setCurrentRequestedSatelliteId] = useState<string | null>(null);

  const handleControllerReady = useCallback((cesiumController: CesiumController) => {
    setCesiumController(cesiumController);
  }, []);

  const checkSatelliteCount = useCallback(() => {
    if (cesiumController) {
      const isLimitedMode = cesiumController.checkSatelliteCountAndSetMode();
      setIsTimelineDisabled(isLimitedMode);
    }
  }, [cesiumController]);

  const handlePlayPauseToggle = useCallback(() => {
    if (cesiumController) {
      if (cesiumController.isInLimitedTrajectoryMode()) {
        Modal.info({
          title: '功能暂时不可用',
          content: '卫星实体数量大于500颗，为保证页面顺畅时间刻度条功能关闭',
          okText: '我知道了',
          maskClosable: true,
        });
        return;
      }

      if (cesiumController.isClockAnimating()) {
        cesiumController.stopClock();
        setIsPlaying(false);
      } else {
        cesiumController.startClock();
        setIsPlaying(true);
      }
    }
  }, [cesiumController]);

  const handleMultiplierChange = (multiplier: number) => {
    if (cesiumController) {
      if (cesiumController.isInLimitedTrajectoryMode()) {
        Modal.info({
          title: '功能暂时不可用',
          content: '卫星实体数量大于500颗，为保证页面顺畅时间刻度条功能关闭',
          okText: '我知道了',
          maskClosable: true,
        });
        return;
      }

      cesiumController.setTimeMultiplier(multiplier);
      setMultiplier(multiplier);
    }
  };

  const handleRealTimeClick = useCallback(() => {
    if (cesiumController) {
      if (cesiumController.isInLimitedTrajectoryMode()) {
        Modal.info({
          title: '功能暂时不可用',
          content: '卫星实体数量大于500颗，为保证页面顺畅时间刻度条功能关闭',
          okText: '我知道了',
          maskClosable: true,
        });
        return;
      }

      const currentSystemTime = new Date();
      cesiumController.setCurrentTime(currentSystemTime);
      
      cesiumController.syncAllSatellitesToCurrentTime();
      
      console.log('已将所有卫星同步到当前系统时间:', currentSystemTime.toISOString());
    }
  }, [cesiumController]);

  useEffect(() => {
    if (cesiumController) {
      cesiumController.startClock();
      setIsPlaying(true);
      
      checkSatelliteCount();
    }
  }, [cesiumController, checkSatelliteCount]);

  const handleSatelliteClick = useCallback((satellite: Satellite) => {
    if (!cesiumController || !satellite.id) return;
    
    setSelectedSatellite(satellite);
    
    if (cesiumController.isInLimitedTrajectoryMode()) {
      cesiumController.selectSatelliteForDetailedTrajectory(satellite.id);
    }
  }, [cesiumController]);

  const handleClearSelectedSatellite = useCallback(() => {
    if (!cesiumController) return;
    
    setSelectedSatellite(null);
    
    if (cesiumController.isInLimitedTrajectoryMode()) {
      cesiumController.clearSelectedSatellite();
    }
  }, [cesiumController]);

  const handleLayersClick = () => {
    setLayersPanelVisible(!layersPanelVisible);
    console.log('Layers clicked');
  };

  // 🌟 新增：处理卫星信息请求
  const handleSatelliteInfoRequest = useCallback(async (satelliteId: string, noradId: number) => {
    console.log(`📋 收到卫星信息请求: ${satelliteId}, NORAD ID: ${noradId}`);
    
    // 🌟 重要：每次请求新卫星信息时，先清空之前的数据
    setCurrentRequestedSatelliteId(satelliteId);
    setSatelliteBasicInfo(null);
    setSatelliteBasicInfoLoading(true);
    setSatelliteBasicInfoError(null);
    setSatelliteBasicInfoVisible(true);
    
    console.log('📋 设置面板状态: loading=true, visible=true, 清空之前数据');
    
    try {
      console.log('📋 准备调用API...');
      console.log('📋 API端点: /api/v1/database/filter-satellites');
      console.log('📋 请求参数:', { noradId: noradId, page: 1, limit: 10 });
      
      // 调用API获取卫星详细信息
      const response = await apiService.post<{
        success: boolean;
        results: SatelliteApiData[];
        total: number;
      }>('/api/v1/database/filter-satellites', {
        noradId: noradId,
        page: 1,
        limit: 10
      });
      
      console.log('📋 API响应:', response);
      
      if (response.success && response.results && response.results.length > 0) {
        setSatelliteBasicInfo(response.results[0]);
        console.log('📋 成功获取卫星信息:', response.results[0]);
        console.log('📋 设置面板数据完成');
      } else {
        // 🌟 如果API没有返回数据，创建一个基本的卫星信息对象
        const basicSatelliteInfo: SatelliteApiData = {
          id: Date.now(),
          satellite_name: [{ value: satelliteId, sources: ['local'] }],
          alternative_name: [],
          cospar_id: [],
          country_of_registry: [],
          owner: [],
          status: [{ value: '未知', sources: ['local'] }],
          norad_id: [{ value: noradId, sources: ['local'] }],
          launch_info: [],
          orbit_info: [],
          purpose: [],
          constellation: [],
          users: []
        };
        setSatelliteBasicInfo(basicSatelliteInfo);
        console.warn('📋 未找到卫星详细信息，使用基本信息，响应:', response);
      }
    } catch (error) {
      console.error('📋 获取卫星信息失败:', error);
      console.error('📋 错误详情:', {
        message: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined,
        response: (error as any)?.response?.data
      });
      
      // 🌟 即使API失败，也显示基本的卫星信息
      const basicSatelliteInfo: SatelliteApiData = {
        id: Date.now(),
        satellite_name: [{ value: satelliteId, sources: ['local'] }],
        alternative_name: [],
        cospar_id: [],
        country_of_registry: [],
        owner: [],
        status: [{ value: 'API连接失败', sources: ['local'] }],
        norad_id: [{ value: noradId, sources: ['local'] }],
        launch_info: [],
        orbit_info: [],
        purpose: [],
        constellation: [],
        users: []
      };
      setSatelliteBasicInfo(basicSatelliteInfo);
      setSatelliteBasicInfoError('网络连接失败，显示基本信息');
    } finally {
      setSatelliteBasicInfoLoading(false);
      console.log('📋 设置面板状态: loading=false');
    }
  }, []);

  // 🌟 新增：关闭卫星基础信息面板
  const handleCloseSatelliteBasicInfo = useCallback(() => {
    setSatelliteBasicInfoVisible(false);
    setSatelliteBasicInfo(null);
    setSatelliteBasicInfoError(null);
    setCurrentRequestedSatelliteId(null);
  }, []);

  // 发射场点击处理
  const handleLaunchSiteClick = useCallback((data: any) => {
    console.log('🚀 收到发射场点击事件:', data);

    if (data.type === 'wiki' && data.siteName && data.wikiInfo) {
      // 显示Wiki信息面板
      setLaunchSiteWikiInfo({ siteName: data.siteName, wikiInfo: data.wikiInfo });
      setLaunchSiteWikiVisible(true);
      // 隐藏基本信息面板
      setSelectedLaunchSite(null);
    } else if (data.type === 'basic' && data.site) {
      // 显示基本信息面板
      setSelectedLaunchSite(data.site);
      // 隐藏Wiki面板
      setLaunchSiteWikiVisible(false);
      setLaunchSiteWikiInfo(null);
    }
  }, []);

  // 关闭发射场Wiki面板
  const handleCloseLaunchSiteWiki = useCallback(() => {
    setLaunchSiteWikiVisible(false);
    setLaunchSiteWikiInfo(null);
  }, []);

  useEffect(() => {
    const initController = async () => {
      if (containerRef.current && !cesiumController) {
        try {
          console.log('开始加载服务...');

          // 初始化星座数据管理器
          console.log('🌟 初始化星座数据管理器...');
          await constellationDataManager.init();
          console.log('✅ 星座数据管理器初始化完成');

          // 动态导入所需服务
          const [
            { mockSatellites },
            { mockDebrisData },
            { launchSiteService: launchSiteServiceImpl },
            { groundStationService: groundStationServiceImpl }
          ] = await Promise.all([
            import('../../services/mockSatelliteData'),
            import('../../services/mockDebrisData'),
            import('../../services/mockLaunchSiteData'),
            import('../../services/mockGroundStationData')
          ]);

          // 验证服务是否正确加载
          if (!mockSatellites) {
            throw new Error('卫星服务加载失败');
          }
          if (!mockDebrisData) {
            throw new Error('碎片服务加载失败');
          }
          if (!launchSiteServiceImpl) {
            throw new Error('发射场服务加载失败');
          }
          if (!groundStationServiceImpl) {
            throw new Error('地面站服务加载失败');
          }

          console.log('所有服务加载完成，开始初始化CesiumController...');
          
          // 创建服务包装对象
          const satelliteService = {
            getAllSatellites: () => mockSatellites,
            getSatellitesByConstellation: (constellation: string) => 
              mockSatellites.filter(sat => sat.constellationName === constellation),
            getSatelliteById: (id: string) =>
              mockSatellites.find(sat => sat.id === id)
          };

          const debrisService = {
            getAllDebris: () => Promise.resolve(mockDebrisData),
            getDebrisById: (id: string) => 
              Promise.resolve(mockDebrisData.find(debris => debris.id === id))
          };

          const controller = new CesiumController(
            containerRef.current,
            satelliteService,
            debrisService,
            launchSiteServiceImpl,
            groundStationServiceImpl
          );

          // 设置事件处理器
          if (handleSatelliteClick) {
            controller.setOnSatelliteClick((satellite: Satellite) => {
              handleSatelliteClick(satellite);
            });
          }

          controller.setOnDebrisClick((debris) => {
            setSelectedDebris(debris);
          });

          // 设置发射场点击回调
          controller.setOnLaunchSiteClick(handleLaunchSiteClick);

          // 🌟 新增：设置卫星信息请求回调
          controller.satelliteController.setOnSatelliteInfoRequest(handleSatelliteInfoRequest);

          setCesiumController(controller);
          console.log('CesiumController初始化完成');

        } catch (error) {
          console.error('初始化失败:', error);
          message.error(`初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }
    };

    initController();
  }, [containerRef, cesiumController, handleSatelliteClick, handleSatelliteInfoRequest, handleLaunchSiteClick]);

  // 通过NORAD ID处理卫星显示的函数
  const handleSatelliteByNoradId = useCallback(async (noradIdStr: string) => {
    if (!cesiumController) {
      console.error('CesiumController not initialized');
      return;
    }

    try {
      const noradIdNum = parseInt(noradIdStr, 10);
      if (isNaN(noradIdNum) || noradIdNum <= 0) {
        console.error('Invalid NORAD ID:', noradIdStr);
        message.error('无效的NORAD ID');
        return;
      }

      console.log(`🔍 开始通过NORAD ID搜索卫星: ${noradIdNum}`);

      const { tleDataManager } = await import('../../services/tleDataManager');
      
      const allTleData = await tleDataManager.getTleData();

      if (allTleData.length === 0) {
        console.error('未获取到TLE数据');
        message.error('未获取到TLE数据，请检查网络连接');
        return;
      }

      const foundSatellite = allTleData.find(tle => tle.norad_id === noradIdNum);

      if (!foundSatellite) {
        console.warn(`未找到NORAD ID为 ${noradIdNum} 的卫星`);
        message.warning(`未找到NORAD ID为 ${noradIdNum} 的卫星`);
        return;
      }

      console.log(`✅ 找到卫星:`, foundSatellite);

      const tleLines = foundSatellite.tle_raw.split('\n');
      let line1 = '', line2 = '';

      for (const line of tleLines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('1 ')) {
          line1 = trimmedLine;
        } else if (trimmedLine.startsWith('2 ')) {
          line2 = trimmedLine;
        }
      }

      if (!line1 || !line2) {
        console.error('TLE数据格式错误，无法解析');
        message.error('TLE数据格式错误，无法解析');
        return;
      }

      const satelliteData = [{
        id: foundSatellite.norad_id.toString(),
        name: foundSatellite.satellite_name || `Satellite-${foundSatellite.norad_id}`,
        line1,
        line2,
        constellation: 'Single-Satellite'
      }];

      console.log(`🚀 开始显示单个卫星:`, satelliteData[0]);

      cesiumController.hideAllSatellites();

      await cesiumController.hybridRenderSatellites(satelliteData);

      message.success(`成功显示卫星: ${satelliteData[0].name} (NORAD ID: ${noradIdNum})`);

    } catch (error) {
      console.error('通过NORAD ID搜索卫星失败:', error);
      message.error('搜索卫星失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }, [cesiumController]);

  useEffect(() => {
    if (!cesiumController) {
      console.log('CesiumController not initialized yet');
      return;
    }

    const type = searchParams.get('type');
    const id = searchParams.get('id');
    const tle1 = searchParams.get('tle1');
    const tle2 = searchParams.get('tle2');
    const noradId = searchParams.get('noradId');
    const longitude = searchParams.get('longitude');
    const latitude = searchParams.get('latitude');
    const altitude = searchParams.get('altitude');

    console.log('URL Parameters:', { type, id, tle1, tle2, noradId, longitude, latitude, altitude });

    if (!type || !id) {
      console.log('Missing required parameters: type or id');
      return;
    }

    if (['satellite', 'debris', 'spacestation'].includes(type)) {
      if (noradId) {
        console.log('Processing NORAD ID parameter:', noradId);
        handleSatelliteByNoradId(noradId);
        return;
      }
      
      if (!tle1 || !tle2) {
        console.log('Missing TLE data for space object');
        return;
      }
      console.log('Locating space object:', { type, id, tle1, tle2 });
      cesiumController.locateSpaceObject(id, type, { tle1, tle2 }).catch(error => {
        console.error('Error locating space object:', error);
      });
    }
    else if (['launchsite', 'groundstation'].includes(type)) {
      if (!longitude || !latitude || !altitude) {
        console.log('Missing position data for ground object');
        return;
      }
      const position = {
        longitude: parseFloat(longitude),
        latitude: parseFloat(latitude),
        altitude: parseFloat(altitude)
      };
      console.log('Locating ground object:', { type, id, position });
      cesiumController.locateGroundObject({ id, type, position }).catch(error => {
        console.error('Error locating ground object:', error);
      });
    } else {
      console.log('Unknown object type:', type);
    }
  }, [cesiumController, searchParams, handleSatelliteByNoradId]);

  const handleViewSatelliteDetails = useCallback(() => {
    // 处理查看卫星详情的逻辑
    if (selectedSatellite) {
      console.log('View satellite details:', selectedSatellite);
    }
  }, [selectedSatellite]);

  // 为图层面板提供控制器
  const getCesiumControllerForLayersPanel = () => {
    return cesiumController || undefined;
  };

  return (
    <PageContainer>
      <Header />
      <MainContent>
        <div ref={containerRef} className="cesium-container" />
        <SideButtons
          onLayersClick={handleLayersClick}
          layersPanelVisible={layersPanelVisible}
        />
        {cesiumController && (
          <TimeController
            currentTime={new Date(currentTime)}
            multiplier={multiplier}
            isPlaying={isPlaying}
            isDisabled={isTimelineDisabled}
            onMultiplierChange={handleMultiplierChange}
            onPlayPauseToggle={handlePlayPauseToggle}
            onRealTimeClick={handleRealTimeClick}
          />
        )}
        {/* 图层面板 */}
        {layersPanelVisible && (
          <LayersPanel 
            visible={layersPanelVisible} 
            cesiumController={getCesiumControllerForLayersPanel()}
          />
        )}
        {selectedSatellite && (
          <SatelliteInfoPanel
            satellite={selectedSatellite}
            onClose={handleClearSelectedSatellite}
            onViewDetails={handleViewSatelliteDetails}
          />
        )}
        {selectedDebris && (
          <DebrisInfoPanel
            debris={selectedDebris}
            onClose={() => setSelectedDebris(null)}
          />
        )}
        {selectedLaunchSite && (
          <LaunchSiteInfoPanel
            launchSite={selectedLaunchSite}
            onClose={() => setSelectedLaunchSite(null)}
          />
        )}
        {/* 发射场Wiki信息面板 */}
        {launchSiteWikiVisible && launchSiteWikiInfo && (
          <LaunchSiteWikiPanel
            siteName={launchSiteWikiInfo.siteName}
            wikiInfo={launchSiteWikiInfo.wikiInfo}
            onClose={handleCloseLaunchSiteWiki}
          />
        )}
        {/* 🌟 新增：卫星基础信息面板 */}
        <SatelliteBasicInfoPanel
          satelliteData={satelliteBasicInfo}
          loading={satelliteBasicInfoLoading}
          error={satelliteBasicInfoError}
          visible={satelliteBasicInfoVisible}
          onClose={handleCloseSatelliteBasicInfo}
        />
      </MainContent>
    </PageContainer>
  );
} 