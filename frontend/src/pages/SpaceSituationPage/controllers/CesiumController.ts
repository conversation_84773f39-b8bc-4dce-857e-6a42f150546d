import * as Cesium from "cesium";
import { SatelliteController } from './SatelliteController';
import { LightSatelliteRenderer, LightSatelliteData } from './LightSatelliteRenderer';
import { HybridSatelliteRenderer, SatelliteData } from './HybridSatelliteRenderer';
import { LaunchSite, LaunchSiteApiResponse, convertApiDataToLaunchSite } from '../../../types/launchSite';
import { Satellite } from '../../../types/satellite';
import { SpaceDebris } from '../../../types/debris';
import { OrbitCalculator } from '../../../services/orbitCalculator';
import { apiService } from '../../../services/apiService';
import { message } from 'antd';

interface SpaceObjectParams {
  id: string;
  type: string;
  tle: {
    line1: string;
    line2: string;
  };
}

interface GroundObjectParams {
  id: string;
  type: string;
  position: {
    longitude: number;
    latitude: number;
    altitude: number;
  };
}

export class CesiumController {
  private viewer: Cesium.Viewer;
  private scene: Cesium.Scene;
  private camera: Cesium.Camera;
  private clock: Cesium.Clock;
  private rotationEventHandler: Cesium.EventCallback;
  private lastRotationTime: Date;
  public satelliteController: SatelliteController;
  public lightSatelliteRenderer: LightSatelliteRenderer;
  public hybridSatelliteRenderer: HybridSatelliteRenderer;
  private visibilityHandler: () => void;
  private onSatelliteClick?: (satellite: Satellite) => void;
  private onDebrisClick?: (debris: SpaceDebris) => void;
  private launchSiteEntities: Map<string, Cesium.Entity> = new Map();
  private launchSites: Map<string, LaunchSite> = new Map();
  private onLaunchSiteClick?: (launchSite: LaunchSite) => void;
  private satelliteService: any;  // 卫星服务
  private debrisService: any;     // 碎片服务
  private launchSiteService: any; // 发射场服务
  private groundStationService: any; // 地面站服务
  private earthRotationEnabled: boolean = true; // 添加地球自转状态变量，默认开启
  private clockTickListener: Cesium.EventCallback;
  private satelliteDataCache: Map<string, {
    lastCalculationTime: Date;
    nextCalculationTime: Date;
    calculating: boolean;
  }> = new Map();
  private readonly PRELOAD_WINDOW_MINUTES = 30; // 预加载窗口大小（分钟）
  private readonly RECALCULATION_THRESHOLD_MINUTES = 5; // 触发重新计算的阈值（分钟）
  private readonly TIME_WINDOW_HOURS = 12; // 位置数据计算窗口（小时）
  // 在CesiumController类中添加新的成员变量，用于跟踪上次轨道更新时间
  private lastOrbitUpdateTimes = new Map<string, Date>();
  // 添加LOD管理相关的属性
  private readonly LOD_NEAR_DISTANCE = 1000000; // 近距离阈值：100万米，所有细节可见
  private readonly LOD_MID_DISTANCE = 8000000; // 中距离阈值：800万米，部分细节可见
  private readonly LOD_FAR_DISTANCE = 20000000; // 远距离阈值：2000万米，最小化显示
  private readonly MAX_VISIBLE_SATELLITES = 300; // 最大同时可见卫星数，从500降至300
  private readonly MAX_VISIBLE_SATELLITES_WITH_LABELS = 100; // 最大可见标签卫星数，从200降至100
  private readonly MAX_VISIBLE_ORBITS = 25; // 最大可见轨道数量，从50降至25
  private lodUpdateTimeout: any = null; // 用于防抖的超时处理器
  private lastCameraPosition: Cesium.Cartesian3 | null = null; // 上次相机位置
  private cameraMovementThreshold = 1000000; // 相机移动阈值，单位米，从500000增加到1000000
  // 轨道和标签可见性全局设置
  private orbitsVisible: boolean = false; // 轨道可见性全局设置，修改为默认关闭
  private labelsVisible: boolean = true; // 标签可见性全局设置
  // 传感器可见性设置
  private sensorsVisible: boolean = false;
  // 限制轨迹模式相关属性
  private isLimitedTrajectoryMode: boolean = false;
  // 选中的卫星ID（用于详细轨迹显示）
  private selectedSatelliteId: string | null = null;

  // 鼠标悬停功能相关属性
  private hoveredSatelliteId: string | null = null; // 当前悬停的卫星ID
  private hoverOrbitEntity: Cesium.Entity | null = null; // 悬停时显示的轨道实体
  private hoverLabelEntity: Cesium.Entity | null = null; // 悬停时显示的标签实体
  private hoverTimeout: any = null; // 悬停延迟定时器
  private readonly HOVER_DELAY = 300; // 悬停延迟时间（毫秒）
  private mouseHandler: Cesium.ScreenSpaceEventHandler | null = null; // 鼠标事件处理器

  constructor(
    container: string | HTMLElement,
    satelliteService: any,
    debrisService: any,
    launchSiteService: any,
    groundStationService: any
  ) {
    try {
      console.log('初始化CesiumController...');
      // 保存服务实例
      this.satelliteService = satelliteService;
      this.debrisService = debrisService;
      this.launchSiteService = launchSiteService;
      this.groundStationService = groundStationService;

      // 设置 Cesium ion token
      Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI0NmRjOGRhOS0yZDA3LTRiNjUtOTllMS02ZGFiMjU1MDcxMjkiLCJpZCI6MjY4MTY4LCJpYXQiOjE3MzY1MTA4MzJ9.SrzY6j9VoLzjBjxMRyrjJEWXliomq-_5uZGGcEEXLfc';

      // 初始化 viewer
      this.viewer = new Cesium.Viewer(container, {
        animation: false,
        timeline: true,
        fullscreenButton: false,
        baseLayerPicker: false,
        geocoder: false,
        homeButton: false,
        infoBox: false,
        sceneModePicker: false,
        selectionIndicator: false,
        navigationHelpButton: false,
        creditContainer: document.createElement('div'),
        // 禁用自动跟踪
        automaticallyTrackDataSourceClocks: false, // 修正属性名
        // 自定义时间轴样式
        timelineStyle: {
          backgroundColor: 'rgba(0, 0, 0, 0.3)',  // 背景色
          backgroundImage: 'none',                 // 移除背景图
          borderColor: 'rgba(255, 255, 255, 0.1)', // 边框颜色
          gradientStops: [                         // 渐变色
            'rgba(0, 0, 0, 0.3)',
            'rgba(0, 0, 0, 0.3)'
          ],
          textColor: 'white',                      // 文字颜色
          fontSize: '12px',                        // 文字大小
          highlightColor: '#1890ff'                // 高亮颜色
        }
      });

      // 移除 Cesium ion 水印
      if (this.viewer.cesiumWidget) {
        this.viewer.cesiumWidget.creditContainer.style.display = 'none';
      }

      // 调整时间轴样式
      const timeline = this.viewer.timeline.container;
      if (timeline instanceof HTMLElement) { // 修正Element类型检查
        // 找到时间轴的父容器
        const timelineContainer = timeline.parentElement;
        if (timelineContainer instanceof HTMLElement) { // 修正Element类型检查
          timelineContainer.style.position = 'absolute';
          timelineContainer.style.left = '0';
          timelineContainer.style.right = '80px';  // 改为按钮宽度
          timelineContainer.style.bottom = '0';
          timelineContainer.style.width = 'auto';
        }

        // 修改时间轴本身的样式
        timeline.style.width = '100%';
        timeline.style.height = '32px';
        timeline.style.padding = '0';
        timeline.style.margin = '0';
        timeline.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
        timeline.style.border = '1px solid rgba(255, 255, 255, 0.1)';
        timeline.style.borderRight = 'none';  // 移除右边框，与按钮无缝连接
        
        // 调整时间轴内部元素的样式
        const timelineTrack = timeline.querySelector('.cesium-timeline-track');
        if (timelineTrack instanceof HTMLElement) { // 修正Element类型检查
          timelineTrack.style.height = '32px';
          timelineTrack.style.top = '0';
          timelineTrack.style.width = '100%';
        }
      }

      this.scene = this.viewer.scene;
      this.camera = this.viewer.camera;
      this.clock = this.viewer.clock;

      // 初始化基础设置
      this.initializeBasicSettings();
      
      // 设置默认视角
      this.setDefaultView();

      // 立即初始化图层 - 删除setTimeout
      this.initializeImageryLayers().then(() => {
        console.log('地球图层初始化完成');
      }).catch(error => {
        console.error('地球图层初始化失败:', error);
      });

      // 设置时钟初始配置
      this.initializeClock();

      this.lastRotationTime = new Date();

      // 初始化卫星控制器
      this.satelliteController = new SatelliteController(this.viewer);
      
      // 初始化轻量级卫星渲染器
      this.lightSatelliteRenderer = new LightSatelliteRenderer(this.viewer, this.satelliteController);
      
      // 设置SatelliteController中的lightSatelliteRenderer引用
      this.satelliteController.setLightSatelliteRenderer(this.lightSatelliteRenderer);
      
      // 初始化混合卫星渲染器
      this.hybridSatelliteRenderer = new HybridSatelliteRenderer(
        this.viewer,
        this, // 传入当前CesiumController作为快速渲染器
        '/satellite-tiles' // 3D Tiles基础URL
      );
      
      // 确保CesiumController和SatelliteController的轨道可见性状态一致
      // SatelliteController默认轨道不可见(false)，所以这里也设为false
      this.orbitsVisible = false;

      // 确保不会自动跟踪实体
      this.viewer.trackedEntity = undefined;
      this.viewer.scene.screenSpaceCameraController.enableInputs = true;
      
      // 启用自动更新卫星位置的监听器
      this.setupClockListener(); // 在构造函数中初始化时钟监听器

      // 添加页面可见性监听
      this.visibilityHandler = () => {
        if (document.visibilityState === 'visible') {
          // 恢复场景渲染
          this.viewer.scene.requestRender();
          // 强制更新一次所有卫星位置
          this.viewer.clock.currentTime = this.viewer.clock.currentTime;
          // 确保动画继续运行
          this.viewer.clock.shouldAnimate = true;
          // 确保更新监听器是活跃的
          this.setupClockListener();
        }
      };
      document.addEventListener('visibilitychange', this.visibilityHandler);

      // Set up satellite double-click handler
      this.satelliteController.setOnSatelliteDoubleClick((satellite) => {
        console.log('Satellite clicked:', satellite);
        if (this.onSatelliteClick) {
          this.onSatelliteClick(satellite);
        }
      });

      // Set up debris double-click handler
      this.satelliteController.setOnDebrisDoubleClick((debris) => {
        console.log('Debris clicked:', debris);
        if (this.onDebrisClick) {
          this.onDebrisClick(debris);
        }
      });

      console.log('CesiumController初始化完成');

      // 为相机移动添加事件监听器，用于LOD控制
      this.initializeLodControl();

      // 鼠标悬停功能将集成到SatelliteController中
      // this.setupEventHandlers();

    } catch (error) {
      console.error('Error in constructor:', error);
      throw error;
    }
  }

  private initializeScene(): void {
    try {
      // 基础场景配置
      this.initializeBasicSettings();

      // 优化渲染质量
      if (this.scene.postProcessStages && this.scene.postProcessStages.fxaa) {
        this.scene.postProcessStages.fxaa.enabled = false; // 禁用FXAA抗锯齿
      }
      if (this.scene.globe) {
        this.scene.globe.maximumScreenSpaceError = 4; // 增大地形误差阈值(默认为2)
      }

      // 设置默认视角
      this.setDefaultView();

      // 初始化地形和图层
      Promise.all([
        this.initializeTerrain(),
        this.initializeImageryLayers()
      ]).catch(error => {
        console.error('Error during initialization:', error);
      });
    } catch (error) {
      console.error('Error in initializeScene:', error);
    }
  }

  private initializeBasicSettings(): void {
    if (this.scene && this.scene.globe) {
      // 基础场景配置
      this.scene.globe.enableLighting = false;  // 禁用光照
      this.scene.globe.baseColor = Cesium.Color.fromCssColorString('#000000');
      this.scene.backgroundColor = Cesium.Color.fromCssColorString('#000000');

      // 增强大气层效果
      this.scene.skyAtmosphere.show = true;
      this.scene.globe.showGroundAtmosphere = true;
      
      // 优化大气层参数
      const atmosphere = this.scene.skyAtmosphere;
      atmosphere.hueShift = 0.0;
      atmosphere.saturationShift = 0.2;
      atmosphere.brightnessShift = 0.5;
      atmosphere.atmosphereRadius = 1.035;
      atmosphere.atmosphereLightIntensity = 5.0;

      // 调整地球大气层参数
      this.scene.globe.atmosphereLightIntensity = 5.0;
      this.scene.globe.atmosphereHueShift = 0.0;
      this.scene.globe.atmosphereSaturationShift = 0.2;
      this.scene.globe.atmosphereBrightnessShift = 0.5;

      // 优化材质渲染
      this.scene.globe.maximumScreenSpaceError = 1.5;
      this.scene.globe.tileCacheSize = 1000;
      this.scene.globe.preloadSiblings = true;
      this.scene.globe.dynamicAtmosphereLighting = false;  // 禁用动态大气光照
      this.scene.globe.showGroundAtmosphere = true;

      // 增强星空效果
      this.scene.skyBox.show = true;
      this.scene.skyBox.sources = {
        positiveX: '/cesium/Assets/Textures/SkyBox/tycho2t3_80_px.jpg',
        negativeX: '/cesium/Assets/Textures/SkyBox/tycho2t3_80_mx.jpg',
        positiveY: '/cesium/Assets/Textures/SkyBox/tycho2t3_80_py.jpg',
        negativeY: '/cesium/Assets/Textures/SkyBox/tycho2t3_80_my.jpg',
        positiveZ: '/cesium/Assets/Textures/SkyBox/tycho2t3_80_pz.jpg',
        negativeZ: '/cesium/Assets/Textures/SkyBox/tycho2t3_80_mz.jpg'
      };
      
      // 增强星空亮度
      this.scene.skyBox.intensity = 100.0;
      
      // 优化场景渲染
      this.scene.globe.show = true;
      this.scene.globe.backFaceCulling = false;
      this.scene.globe.showSkirts = true;
      
      // 优化抗锯齿
      this.scene.fxaa = true;
      this.scene.postProcessStages.fxaa.enabled = true;
    }
  }

  private setDefaultView(): void {
    this.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(0, 0, 20000000),
      orientation: {
        heading: 0,
        pitch: -Cesium.Math.PI_OVER_TWO,
        roll: 0,
      },
    });
  }

  private async initializeTerrain(): Promise<void> {
    try {
      // 创建地形提供者
      const terrainProvider = await Cesium.CesiumTerrainProvider.fromIonAssetId(1);
      
      // 等待地形提供者准备就绪
      await terrainProvider.readyPromise;
      
      // 设置地形
      if (this.viewer) {
        this.viewer.terrainProvider = terrainProvider;
      }

    } catch (error) {
      console.error('Failed to initialize terrain:', error);
    }
  }

  private async initializeImageryLayers(): Promise<void> {
    try {
      if (!this.viewer) return;

      // 移除默认的影像图层
      this.viewer.imageryLayers.removeAll();
      console.log('已清除所有默认图层，开始加载地球影像');

      // 使用 Cesium Ion 的卫星影像
      try {
        const imageryProvider = await Cesium.IonImageryProvider.fromAssetId(2, {
          maximumLevel: 19,
          minimumLevel: 0
        });

        // 添加基础影像图层
        const baseLayer = this.viewer.imageryLayers.addImageryProvider(imageryProvider);
        console.log('成功加载基础影像图层');
        
        // 设置图层属性
        if (baseLayer) {
          baseLayer.alpha = 0.8;  // 起始透明度稍低，以便更好看到边界
          baseLayer.brightness = 1.1;
          baseLayer.contrast = 1.1;
          baseLayer.gamma = 1.05;
          baseLayer.saturation = 1.1;
          baseLayer.hue = 0.0;
          baseLayer.splitDirection = Cesium.SplitDirection.NONE;
        }
      } catch (error) {
        console.error('加载基础影像图层失败:', error);
      }

      // 添加天地图行政边界图层
      try {
        const boundaryProvider = new Cesium.WebMapTileServiceImageryProvider({
          url: "https://t{s}.tianditu.gov.cn/ibo_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=ibo&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&tk=c1797ef929be4234b209b4e04c682441",
          layer: "ibo",
          style: "default",
          format: "tiles",
          tileMatrixSetID: "w",
          subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
          maximumLevel: 18
        });

        // 添加边界图层
        const boundaryLayer = this.viewer.imageryLayers.addImageryProvider(boundaryProvider);
        console.log('成功加载行政边界图层');
        
        if (boundaryLayer) {
          boundaryLayer.alpha = 0.6;  // 透明度稍高
          boundaryLayer.brightness = 2.0;  // 增强边界亮度
          boundaryLayer.contrast = 1.5;    // 增强对比度
          boundaryLayer.gamma = 1.0;
        }
      } catch (error) {
        console.error('加载行政边界图层失败:', error);
      }

      // 创建中文标注图层
      try {
        const labelImageryProvider = new Cesium.WebMapTileServiceImageryProvider({
          url: "https://t{s}.tianditu.gov.cn/cva_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cva&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&tk=c1797ef929be4234b209b4e04c682441",
          layer: "cva",
          style: "default",
          format: "tiles",
          tileMatrixSetID: "w",
          subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
          maximumLevel: 18
        });

        // 添加中文标注
        const labelLayer = this.viewer.imageryLayers.addImageryProvider(labelImageryProvider);
        console.log('成功加载中文标注图层');
        
        if (labelLayer) {
          labelLayer.alpha = 0.7;  // 透明度适中
        }
      } catch (error) {
        console.error('加载中文标注图层失败:', error);
      }

      // 使用渐变显示所有图层
      console.log('开始渐变显示所有图层');
      const imageryLayers = this.viewer.imageryLayers;
      let currentAlpha = 0;
      const targetAlphas = [0.8, 0.6, 0.7]; // 各图层目标透明度
      
      const fadeIn = () => {
        if (currentAlpha < 1) {
          currentAlpha += 0.05;
          
          // 为每个图层设置逐渐增加的透明度
          for (let i = 0; i < Math.min(imageryLayers.length, targetAlphas.length); i++) {
            const layer = imageryLayers.get(i);
            if (layer) {
              layer.alpha = Math.min(currentAlpha, targetAlphas[i]);
            }
          }
          
          // 继续渐变
          requestAnimationFrame(fadeIn);
        } else {
          console.log('图层渐变显示完成');
        }
      };
      
      // 开始渐变
      requestAnimationFrame(fadeIn);

    } catch (error) {
      console.error('初始化图层失败:', error);
      // 如果图层加载失败，至少设置基础颜色避免黑屏
      if (this.viewer?.scene?.globe) {
        this.scene.globe.baseColor = Cesium.Color.fromCssColorString('#2B2B6B');
        console.log('设置基础颜色为深蓝色');
      }
    }
  }

  private initializeClock(): void {
    // 设置时钟的初始时间范围
    const now = new Date();
    const start = new Date(now);
    start.setHours(now.getHours() - 6);  // 默认显示前后6小时
    const stop = new Date(now);
    stop.setHours(now.getHours() + 6);

    // 配置时钟
    this.clock.startTime = Cesium.JulianDate.fromDate(start);
    this.clock.currentTime = Cesium.JulianDate.fromDate(now);
    this.clock.stopTime = Cesium.JulianDate.fromDate(stop);
    this.clock.clockRange = Cesium.ClockRange.CLAMPED;
    // 修改：使用SYSTEM_CLOCK_MULTIPLIER以正确控制卫星速度
    this.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK_MULTIPLIER;
    this.clock.multiplier = 1.0;  // 确保使用准确的实时倍率
    this.clock.shouldAnimate = true;  // 默认启动时钟

    // 配置时间轴
    if (this.viewer.timeline) {
      // 设置时间轴样式和初始范围
      this.viewer.timeline.zoomTo(
        this.clock.startTime,
        this.clock.stopTime
      );

      // 监听时间轴变化
      this.viewer.timeline.addEventListener('settime', () => {
        const currentTime = Cesium.JulianDate.toDate(this.clock.currentTime);
        this.updateEarthRotation(currentTime);
        this.handleTimelineExtension(currentTime);
      }, false);
    }

    // 初始化地球自转
    this.initializeEarthRotation();
    
    console.log("已初始化时钟: 模式=SYSTEM_CLOCK_MULTIPLIER, 倍率=1.0, 时间范围=", {
      start: start.toISOString(),
      current: now.toISOString(),
      stop: stop.toISOString()
    });
  }

  // 处理时间轴延展
  private handleTimelineExtension(currentTime: Date): void {
    if (!this.clock || !this.viewer.timeline) return;

    const startTime = Cesium.JulianDate.toDate(this.clock.startTime);
    const stopTime = Cesium.JulianDate.toDate(this.clock.stopTime);
    const now = new Date();
    
    // 计算最大范围（前后3天）
    const maxStart = new Date(now);
    maxStart.setDate(now.getDate() - 3);
    const maxEnd = new Date(now);
    maxEnd.setDate(now.getDate() + 3);

    // 计算当前时间到边界的距离（毫秒）
    const distanceToStart = currentTime.getTime() - startTime.getTime();
    const distanceToEnd = stopTime.getTime() - currentTime.getTime();
    const threshold = 1800000; // 30分钟的毫秒数，作为触发延展的阈值

    let needUpdate = false;
    let newStart = new Date(startTime);
    let newEnd = new Date(stopTime);

    // 如果接近开始时间，向前延展
    if (distanceToStart < threshold) {
      const extensionHours = 1; // 每次延展1小时
      newStart = new Date(startTime.getTime() - extensionHours * 3600000);
      if (newStart < maxStart) {
        newStart = new Date(maxStart);
      }
      // 同时移动结束时间，保持显示窗口大小
      newEnd = new Date(newStart.getTime() + 12 * 3600000);
      needUpdate = true;
    }

    // 如果接近结束时间，向后延展
    if (distanceToEnd < threshold) {
      const extensionHours = 1; // 每次延展1小时
      newEnd = new Date(stopTime.getTime() + extensionHours * 3600000);
      if (newEnd > maxEnd) {
        newEnd = new Date(maxEnd);
      }
      // 同时移动开始时间，保持显示窗口大小
      newStart = new Date(newEnd.getTime() - 12 * 3600000);
      needUpdate = true;
    }

    // 如果需要更新时间范围
    if (needUpdate) {
      // 更新时钟的总范围
      this.clock.startTime = Cesium.JulianDate.fromDate(newStart);
      this.clock.stopTime = Cesium.JulianDate.fromDate(newEnd);
      this.clock.currentTime = Cesium.JulianDate.fromDate(currentTime);
      
      // 计算显示窗口的范围（固定12小时）
      const windowStart = new Date(currentTime.getTime() - 6 * 3600000);
      const windowEnd = new Date(currentTime.getTime() + 6 * 3600000);
      
      // 更新时间轴显示范围
      if (this.viewer.timeline) {
        this.viewer.timeline.zoomTo(
          Cesium.JulianDate.fromDate(windowStart),
          Cesium.JulianDate.fromDate(windowEnd)
        );
      }
      
      this.lastRotationTime = currentTime;
    }
  }

  private updateEarthRotation(newTime: Date): void {
    if (!this.lastRotationTime || !this.earthRotationEnabled) { // 当禁用地球自转时，不执行旋转
      this.lastRotationTime = newTime;
      return;
    }

    const timeDiff = (newTime.getTime() - this.lastRotationTime.getTime()) / 1000;
    if (timeDiff !== 0) {
      const rotationRateRadians = (2 * Math.PI) / (24 * 60 * 60);
      // 修复：去掉负号，让地球正确地自西向东自转
      const rotationAngle = rotationRateRadians * timeDiff;
      this.scene.camera.rotate(Cesium.Cartesian3.UNIT_Z, rotationAngle);
      this.lastRotationTime = newTime;
    }
  }

  private initializeEarthRotation(): void {
    const rotationRateRadians = (2 * Math.PI) / (24 * 60 * 60);
    
    this.rotationEventHandler = this.scene.preUpdate.addEventListener(() => {
      if (this.clock?.shouldAnimate) {
        const currentTime = Cesium.JulianDate.toDate(this.clock.currentTime);
        this.updateEarthRotation(currentTime);
      }
    });
  }

  /**
   * 设置时间流速乘数（控制卫星运动速度）
   * @param multiplier 时间流速乘数
   */
  public setTimeMultiplier(multiplier: number): void {
    if (this.viewer && this.viewer.clock) {
      console.log(`设置时间流速乘数: ${multiplier}`);
      
      // 确保 multiplier 是有效的数字
      const validMultiplier = Math.max(0, Math.min(100, multiplier));
      
      // 保存当前时钟模式，确保不被改变
      const currentClockStep = this.viewer.clock.clockStep;
      
      // 设置倍率
      this.viewer.clock.multiplier = validMultiplier;
      
      // 恢复原来的时钟模式
      this.viewer.clock.clockStep = currentClockStep;
      
      // 当速度为 0 时暂停时钟
      if (validMultiplier === 0) {
        this.viewer.clock.shouldAnimate = false;
      } else if (!this.viewer.clock.shouldAnimate) {
        this.viewer.clock.shouldAnimate = true;
      }
      
      // 更新时间轴的显示
      if (this.viewer.timeline) {
        this.viewer.timeline.zoomTo(
          this.viewer.clock.startTime,
          this.viewer.clock.stopTime
        );
      }
      
      console.log(`时钟设置更新: 倍率=${validMultiplier}, 模式=${this.viewer.clock.clockStep}`);
    }
  }

  public startClock(): void {
    if (this.clock) {
      this.clock.shouldAnimate = true;
    }
  }

  public stopClock(): void {
    if (this.clock) {
      this.clock.shouldAnimate = false;
    }
  }

  public get cesiumViewer(): Cesium.Viewer {
    return this.viewer;
  }

  public destroy(): void {
    // 清理悬停相关资源
    this.clearHoverDisplay();
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
      this.hoverTimeout = null;
    }
    
    // 销毁鼠标事件处理器
    if (this.mouseHandler) {
      this.mouseHandler.destroy();
      this.mouseHandler = null;
    }
    
    if (this.rotationEventHandler) {
      this.rotationEventHandler();  // 移除事件处理器
    }
    
    if (this.visibilityHandler) {
      document.removeEventListener('visibilitychange', this.visibilityHandler);
    }
    
    // 销毁轻量级卫星渲染器
    if (this.lightSatelliteRenderer) {
      this.lightSatelliteRenderer.destroy();
    }
    
    // 销毁卫星控制器
    if (this.satelliteController) {
      this.satelliteController.destroy();
    }
    
    if (this.viewer) {
      this.viewer.destroy();
      this.viewer = null;
      this.scene = null;
      this.camera = null;
      this.clock = null;
    }
  }

  public setTimeRange(startDate: Date, endDate: Date): void {
    if (!this.clock) return;
    
    const julianStart = Cesium.JulianDate.fromDate(startDate);
    const julianEnd = Cesium.JulianDate.fromDate(endDate);
    
    this.clock.startTime = julianStart;
    this.clock.stopTime = julianEnd;
    
    // 确保当前时间在新范围内
    if (Cesium.JulianDate.lessThan(this.clock.currentTime, julianStart)) {
      this.clock.currentTime = julianStart;
    } else if (Cesium.JulianDate.greaterThan(this.clock.currentTime, julianEnd)) {
      this.clock.currentTime = julianEnd;
    }
    
    // 更新时间轴显示范围
    if (this.viewer.timeline) {
      this.viewer.timeline.zoomTo(julianStart, julianEnd);
    }
    
    this.lastRotationTime = Cesium.JulianDate.toDate(this.clock.currentTime);
  }

  public getTimeRange(): { start: Date; end: Date } {
    return {
      start: Cesium.JulianDate.toDate(this.clock.startTime),
      end: Cesium.JulianDate.toDate(this.clock.stopTime)
    };
  }

  public getMultiplier(): number {
    return this.clock?.multiplier || 1;
  }

  public getCurrentTime(): Date {
    return this.clock ? Cesium.JulianDate.toDate(this.clock.currentTime) : new Date();
  }

  public isClockAnimating(): boolean {
    return this.clock?.shouldAnimate || false;
  }

  /**
   * 显示指定星座的卫星
   */
  async showConstellation(constellationName: string) {
    try {
      // 保存当前相机状态
      const cameraState = {
        position: this.camera.position.clone(),
        heading: this.camera.heading,
        pitch: this.camera.pitch,
        roll: this.camera.roll
      };

      // 禁用自动跟踪和相机控制
      this.viewer.trackedEntity = undefined;
      const controller = this.viewer.scene.screenSpaceCameraController;
      const wasEnabled = controller.enableInputs;
      controller.enableInputs = false;

      // 添加卫星前确保视角锁定
      this.camera.setView({
        destination: cameraState.position,
        orientation: {
          heading: cameraState.heading,
          pitch: cameraState.pitch,
          roll: cameraState.roll
        }
      });

      // 添加卫星，使用this.satelliteService而不是导入的satelliteService
      // 并添加hasTleData参数，确保只获取有TLE数据的卫星
      const satellites = await this.satelliteService.getSatellitesByConstellation(constellationName, true);
      for (const satellite of satellites) {
        this.satelliteController.addSatellite(satellite);
        // 每添加一个卫星后都确保视角不变
        this.camera.setView({
          destination: cameraState.position,
          orientation: {
            heading: cameraState.heading,
            pitch: cameraState.pitch,
            roll: cameraState.roll
          }
        });
      }

      // 恢复相机控制
      controller.enableInputs = wasEnabled;
      controller.enableZoom = true;
      controller.enableTranslate = true;
      controller.enableTilt = true;
      controller.enableLook = true;

      // 最后再次确保视角正确
      this.camera.setView({
        destination: cameraState.position,
        orientation: {
          heading: cameraState.heading,
          pitch: cameraState.pitch,
          roll: cameraState.roll
        }
      });

    } catch (error) {
      console.error('Error showing constellation:', error);
      throw error;
    }
  }

  /**
   * 隐藏指定星座的卫星
   */
  async hideConstellation(constellationName: string) {
    const satellites = await this.satelliteService.getSatellitesByConstellation(constellationName, true);
    satellites.forEach(satellite => {
      this.satelliteController.removeSatellite(satellite.id);
    });
  }

  /**
   * 隐藏所有卫星
   * 用于处理"全部卫星"图层的隐藏操作
   */
  public hideAllSatellites(): void {
    console.log('🗑️ 开始清除所有卫星');
    
    try {
      // 清除混合渲染器（包括3D Tiles和快速渲染）
      this.hybridSatelliteRenderer.clearAll();
      console.log('✅ 已清除混合渲染器的卫星');
      
      // 清除Entity模式的卫星（SatelliteController管理的卫星）
      this.satelliteController.clearAll();
      console.log('✅ 已清除Entity模式的卫星');
      
      // 清除PointPrimitive模式的卫星（LightSatelliteRenderer管理的卫星）
      this.lightSatelliteRenderer.removeAll();
      console.log('✅ 已清除PointPrimitive模式的卫星');
      
      console.log('🎉 所有卫星已成功清除');
    } catch (error) {
      console.error('❌ 清除卫星时发生错误:', error);
    }
  }

  /**
   * 快速渲染所有卫星（高性能模式）
   * 使用PointPrimitive一次性渲染大量卫星，避免分批处理
   */
  /**
   * 使用混合渲染器渲染卫星（3D Tiles优先，自动降级）
   */
  public async hybridRenderSatellites(tleDataList?: Array<{
    id: string;
    name: string;
    line1: string;
    line2: string;
    constellation?: string;
  }>): Promise<void> {
    console.log('🚀 开始混合渲染卫星');
    
    try {
      // 转换数据格式
      const satelliteData: SatelliteData[] | undefined = tleDataList?.map(item => ({
        id: item.id,
        name: item.name,
        line1: item.line1,
        line2: item.line2,
        constellation: item.constellation
      }));
      
      // 使用混合渲染器
      await this.hybridSatelliteRenderer.renderSatellites(satelliteData);
      
      console.log('✅ 混合渲染完成');
      
    } catch (error) {
      console.error('❌ 混合渲染失败:', error);
      message.error('卫星渲染失败，请检查网络连接或数据格式');
      throw error;
    }
  }

  public async fastRenderAllSatellites(tleDataList: Array<{
    id: string;
    name: string;
    line1: string;
    line2: string;
    constellation?: string;
  }>): Promise<void> {
    console.log('🚀 开始快速渲染模式，卫星数量:', tleDataList.length);
    const startTime = performance.now();
    
    try {
      // 清除现有卫星
      this.hideAllSatellites();
      
      // 导入satellite.js
      const satellite = await import('satellite.js');
      
      // 获取当前时间
      const now = new Date();
      
      // 并行计算所有卫星的当前位置
      const satellitePositions = await Promise.all(
        tleDataList.map(async (tleData) => {
          try {
            // 解析TLE数据
            const satrec = satellite.twoline2satrec(tleData.line1, tleData.line2);
            
            // 计算当前位置
            const positionAndVelocity = satellite.propagate(satrec, now);
            
            if (positionAndVelocity.position && typeof positionAndVelocity.position === 'object') {
              const positionEci = positionAndVelocity.position as any;
              
              // 🌟 严格验证位置数据，避免RangeError
              if (!positionEci || 
                  typeof positionEci.x !== 'number' || 
                  typeof positionEci.y !== 'number' || 
                  typeof positionEci.z !== 'number' ||
                  !isFinite(positionEci.x) || 
                  !isFinite(positionEci.y) || 
                  !isFinite(positionEci.z)) {
                return null; // 跳过无效数据
              }
              
              // 🌟 关键修复：卫星位置也使用惯性坐标系(ECI)，与轨道保持一致
              // 直接使用ECI坐标，不转换为地心地固坐标系
              const position = new Cesium.Cartesian3(
                positionEci.x * 1000, // 转换为米
                positionEci.y * 1000,
                positionEci.z * 1000
              );
              
              // 验证最终位置是否有效
              if (!position || isNaN(position.x) || isNaN(position.y) || isNaN(position.z)) {
                return null; // 跳过无效位置
              }
              
              // 确定星座颜色
              const constellation = tleData.constellation || 'default';
              const color = this.getConstellationColor(constellation);
              
              return {
                id: tleData.id,
                name: tleData.name,
                position: position,
                constellation: constellation,
                color: color,
                tle: {
                  line1: tleData.line1,
                  line2: tleData.line2
                }
              };
            }
            return null;
          } catch (error) {
            console.warn(`计算卫星 ${tleData.id} 位置失败:`, error);
            return null;
          }
        })
      );
      
      // 过滤掉计算失败的卫星
      const validSatellites = satellitePositions.filter(sat => sat !== null);
      
      console.log(`✅ 成功计算 ${validSatellites.length}/${tleDataList.length} 颗卫星位置`);
      
      // 一次性批量添加到LightSatelliteRenderer
      this.lightSatelliteRenderer.addBatch(validSatellites);
      
      const endTime = performance.now();
      const duration = (endTime - startTime) / 1000;
      
      console.log(`🎉 快速渲染完成！耗时: ${duration.toFixed(2)}秒，成功渲染 ${validSatellites.length} 颗卫星`);
      
    } catch (error) {
      console.error('❌ 快速渲染失败:', error);
      throw error;
    }
  }

  /**
   * 获取星座颜色
   */
  private getConstellationColor(constellation: string): Cesium.Color {
    const colors: { [key: string]: Cesium.Color } = {
      'starlink': Cesium.Color.CYAN,
      'oneweb': Cesium.Color.ORANGE,
      'gps': Cesium.Color.GREEN,
      'glonass': Cesium.Color.RED,
      'galileo': Cesium.Color.BLUE,
      'beidou': Cesium.Color.YELLOW,
      'iridium': Cesium.Color.PURPLE,
      'globalstar': Cesium.Color.PINK,
      'default': Cesium.Color.WHITE
    };
    
    return colors[constellation.toLowerCase()] || colors.default;
  }

  /**
   * 显示卫星视场
   */
  async showSatelliteSensors(constellationName: string) {
    // 首先设置全局状态为可见
    this.sensorsVisible = true;

    // 先检查是否有卫星数据
    try {
      const satellites = await this.satelliteService.getSatellitesByConstellation(constellationName, true);
      if (satellites && satellites.length > 0) {
        satellites.forEach(satellite => {
          this.satelliteController.setSatelliteSensorVisibility(satellite.id, true);
        });
        return;
      }
    } catch (error) {
      console.log('No satellites found for sensors display:', error);
    }
    
    // 如果没有找到特定星座的卫星，则显示所有卫星的视场
    this.satelliteController.showAllSatelliteSensors();
  }

  /**
   * 隐藏卫星视场
   */
  async hideSatelliteSensors(constellationName: string) {
    // 设置全局状态为不可见
    this.sensorsVisible = false;

    // 先检查是否有卫星数据
    try {
      const satellites = await this.satelliteService.getSatellitesByConstellation(constellationName, true);
      if (satellites && satellites.length > 0) {
        satellites.forEach(satellite => {
          this.satelliteController.setSatelliteSensorVisibility(satellite.id, false);
        });
        return;
      }
    } catch (error) {
      console.log('No satellites found for sensors hide:', error);
    }
    
    // 如果没有找到特定星座的卫星，则隐藏所有卫星的视场
    this.satelliteController.hideAllSatelliteSensors();
  }

  // 显示所有空间碎片
  showDebris() {
    try {
      // 使用this.debrisService获取碎片数据
      this.debrisService.getAllDebris().then((debris: SpaceDebris[]) => {
        if (debris && debris.length > 0) {
          debris.forEach(item => {
            this.satelliteController.addDebris(item);
          });
        } else {
          console.log('No debris data found');
        }
      }).catch((error: any) => {
        console.error('Error fetching debris data:', error);
      });
    } catch (error) {
      console.error('Error showing debris:', error);
    }
  }

  // 隐藏所有空间碎片
  hideDebris() {
    try {
      // 使用this.debrisService获取碎片数据
      this.debrisService.getAllDebris().then((debris: SpaceDebris[]) => {
        if (debris && debris.length > 0) {
          debris.forEach(item => {
            this.satelliteController.removeDebris(item.id);
          });
        }
      }).catch((error: any) => {
        console.error('Error fetching debris data for removal:', error);
      });
    } catch (error) {
      console.error('Error hiding debris:', error);
    }
  }

  setOnSatelliteClick(handler: (satellite: Satellite) => void) {
    this.onSatelliteClick = handler;
  }

  setOnDebrisClick(handler: (debris: SpaceDebris) => void) {
    this.onDebrisClick = handler;
  }

  // 添加发射场点击事件处理器
  setOnLaunchSiteClick(handler: (launchSite: LaunchSite) => void) {
    this.onLaunchSiteClick = handler;
  }

  // 显示发射场
  async showLaunchSites() {
    try {
      console.log('🚀 开始获取发射场数据...');

      // 调用新的API获取发射场数据
      const response = await apiService.get<LaunchSiteApiResponse>('/api/es/launch/all-launch-sites');

      if (!response.success || !response.data) {
        console.error('获取发射场数据失败:', response);
        message.error('获取发射场数据失败');
        return;
      }

      console.log(`📍 获取到 ${response.data.length} 个发射场数据`);

      // 清除现有的发射场实体
      this.hideLaunchSites();

      // 转换API数据为LaunchSite格式
      const launchSites: LaunchSite[] = [];
      response.data.forEach((apiData, index) => {
        const launchSite = convertApiDataToLaunchSite(apiData, index);
        if (launchSite) {
          launchSites.push(launchSite);
        } else {
          console.warn('无法转换发射场数据:', apiData);
        }
      });

      console.log(`✅ 成功转换 ${launchSites.length} 个发射场数据`);

      // 存储发射场数据
      launchSites.forEach(site => {
        this.launchSites.set(site.id, site);
      });

      // 创建新的发射场实体 - 使用优化的批量创建
      this.createLaunchSiteEntitiesWithOptimization(launchSites);

      // 设置事件处理器
      this.setupEventHandlers();

      message.success(`成功显示 ${launchSites.length} 个发射场`);

    } catch (error) {
      console.error('显示发射场失败:', error);
      message.error('显示发射场失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  // 优化的批量创建发射场实体
  private createLaunchSiteEntitiesWithOptimization(launchSites: LaunchSite[]) {
    // 按地理区域分组，优化标签布局
    const regionGroups = this.groupLaunchSitesByRegion(launchSites);

    // 为每个区域组优化标签偏移
    Object.entries(regionGroups).forEach(([region, sites]) => {
      console.log(`🌍 处理区域 ${region} 的 ${sites.length} 个发射场`);

      // 为密集区域计算优化的标签偏移 - 降低阈值，让更多区域应用优化
      if (sites.length > 1) { // 从3改为1，只要有2个或以上就优化
        this.optimizeLabelLayoutForDenseRegion(sites);
      }

      // 创建实体
      sites.forEach(site => {
        const entity = this.createLaunchSiteEntity(site);
        this.launchSiteEntities.set(site.id, entity);
        this.viewer.entities.add(entity);
      });
    });
  }

  // 按地理区域分组发射场
  private groupLaunchSitesByRegion(launchSites: LaunchSite[]): { [region: string]: LaunchSite[] } {
    const regions: { [region: string]: LaunchSite[] } = {};

    launchSites.forEach(site => {
      const region = this.getRegionForLaunchSite(site.position);
      if (!regions[region]) {
        regions[region] = [];
      }
      regions[region].push(site);
    });

    return regions;
  }

  // 获取发射场所属地理区域
  private getRegionForLaunchSite(position: { longitude: number; latitude: number; altitude: number }): string {
    const { longitude, latitude } = position;

    // 定义主要发射场区域
    if (longitude >= -82 && longitude <= -80 && latitude >= 28 && latitude <= 29) {
      return 'Florida'; // 美国佛罗里达州
    } else if (longitude >= 63 && longitude <= 64 && latitude >= 45.5 && latitude <= 46.5) {
      return 'Baikonur'; // 哈萨克斯坦拜科努尔
    } else if (longitude >= 110.5 && longitude <= 111 && latitude >= 19.5 && latitude <= 20) {
      return 'Wenchang'; // 中国海南文昌
    } else if (longitude >= 99.5 && longitude <= 100.5 && latitude >= 40.5 && latitude <= 41.5) {
      return 'Jiuquan'; // 中国酒泉
    } else if (longitude >= 40 && longitude <= 41 && latitude >= 62.5 && latitude <= 63.5) {
      return 'Plesetsk'; // 俄罗斯普列谢茨克
    } else if (longitude >= -53 && longitude <= -52 && latitude >= 5 && latitude <= 6) {
      return 'Kourou'; // 法属圭亚那库鲁
    } else if (longitude >= -121 && longitude <= -119 && latitude >= 34 && latitude <= 35) {
      return 'Vandenberg'; // 美国范登堡
    } else {
      // 按大洲分组其他区域
      if (longitude >= -180 && longitude <= -30) {
        return 'Americas';
      } else if (longitude >= -30 && longitude <= 60) {
        return 'Europe-Africa';
      } else {
        return 'Asia-Pacific';
      }
    }
  }

  // 为密集区域优化标签布局
  private optimizeLabelLayoutForDenseRegion(sites: LaunchSite[]) {
    // 按纬度排序，从北到南
    sites.sort((a, b) => b.position.latitude - a.position.latitude);

    // 为每个发射场分配不同的标签偏移角度，避免重叠
    sites.forEach((site, index) => {
      const angleStep = 360 / sites.length;
      const angle = index * angleStep;
      const radius = 120 + (index % 5) * 40; // 进一步增大半径和层级间距

      // 计算偏移位置 - 使用更大的偏移范围
      const offsetX = Math.cos(angle * Math.PI / 180) * radius;
      const offsetY = -Math.abs(Math.sin(angle * Math.PI / 180) * radius) - 80; // 确保在图标上方，增大基础距离

      // 存储优化的偏移信息
      (site as any).optimizedOffset = { x: offsetX, y: offsetY };

      console.log(`🎯 发射场 ${site.name} 优化偏移: (${offsetX.toFixed(1)}, ${offsetY.toFixed(1)})`);
    });
  }

  // 隐藏发射场
  hideLaunchSites() {
    this.launchSiteEntities.forEach(entity => {
      this.viewer.entities.remove(entity);
    });
    this.launchSiteEntities.clear();
  }

  // 创建发射场实体
  private createLaunchSiteEntity(site: LaunchSite): Cesium.Entity {
    // 清理和优化显示名称
    const displayName = this.cleanLaunchSiteName(site.name);

    return new Cesium.Entity({
      id: `launch-site-${site.id}`,
      name: displayName,
      position: Cesium.Cartesian3.fromDegrees(
        site.position.longitude,
        site.position.latitude,
        site.position.altitude
      ),
      billboard: {
        image: this.createLaunchSiteIcon(),
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        scale: 0.7,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        disableDepthTestDistance: undefined, // 启用深度测试，避免背面显示
        // 添加图标缩放控制，确保在不同距离下都有合适的大小
        scaleByDistance: new Cesium.NearFarScalar(100000, 1.0, 2000000, 0.6)
      },
      // 使用现代化的标签设计
      label: this.createModernLaunchSiteLabel(displayName, site.position, site)
    });
  }

  // 创建现代化的发射场标签
  private createModernLaunchSiteLabel(displayName: string, position: { longitude: number; latitude: number; altitude: number }, site?: LaunchSite) {
    // 根据名称长度动态调整字体大小和背景
    const nameLength = displayName.length;
    let fontSize = 14;
    let backgroundPadding = new Cesium.Cartesian2(12, 6);

    if (nameLength <= 8) {
      fontSize = 16;
      backgroundPadding = new Cesium.Cartesian2(14, 7);
    } else if (nameLength <= 15) {
      fontSize = 14;
      backgroundPadding = new Cesium.Cartesian2(12, 6);
    } else {
      fontSize = 12;
      backgroundPadding = new Cesium.Cartesian2(10, 5);
    }

    // 计算智能偏移，避免标签重叠
    const smartOffset = this.calculateSmartLabelOffset(position, displayName, site);

    return {
      text: displayName,
      font: `bold ${fontSize}px "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif`,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      fillColor: Cesium.Color.YELLOW, // 使用亮黄色，更醒目
      outlineColor: Cesium.Color.BLACK, // 黑色描边
      outlineWidth: 5, // 更粗的描边

      // 现代化的背景设计 - 使用深色背景
      showBackground: true,
      backgroundColor: new Cesium.Color(0.0, 0.0, 0.0, 0.9), // 深黑色背景
      backgroundPadding: backgroundPadding,

      // 智能定位和偏移
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      pixelOffset: smartOffset,

      // 高度参考和深度测试 - 启用深度测试
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      disableDepthTestDistance: undefined, // 启用深度测试，避免背面显示

      // 智能缩放和透明度控制
      scaleByDistance: new Cesium.NearFarScalar(50000, 1.2, 3000000, 0.7),
      translucencyByDistance: new Cesium.NearFarScalar(50000, 1.0, 8000000, 0.4), // 调整最小透明度

      // 添加像素偏移距离控制，避免近距离时标签过大
      pixelOffsetScaleByDistance: new Cesium.NearFarScalar(50000, 1.0, 1000000, 0.8),

      // 确保标签始终可见
      show: true
    };
  }

  // 计算智能标签偏移，避免重叠
  private calculateSmartLabelOffset(position: { longitude: number; latitude: number; altitude: number }, displayName: string, site?: LaunchSite): Cesium.Cartesian2 {
    // 检查是否有预计算的优化偏移
    if (site && (site as any).optimizedOffset) {
      const optimized = (site as any).optimizedOffset;
      console.log(`🎯 使用优化偏移 ${site.name}: (${optimized.x}, ${optimized.y})`);
      return new Cesium.Cartesian2(optimized.x, optimized.y);
    }

    // 基础偏移
    let baseOffsetX = 0;
    let baseOffsetY = -60; // 在图标上方显示，增加基础距离

    // 根据经纬度计算偏移，避免在密集区域重叠
    const lonHash = Math.abs(position.longitude * 1000) % 100;
    const latHash = Math.abs(position.latitude * 1000) % 100;

    // 检查是否需要避让 - 扩大检查范围
    const needsAvoidance = this.needsLabelAvoidance(position);

    if (needsAvoidance) {
      // 使用位置哈希生成确定性的偏移，避免每次刷新都变化
      const offsetVariationX = (lonHash % 120) - 60; // -60 到 60 的偏移，增大范围
      const offsetVariationY = -80 - (latHash % 80); // -80 到 -160 的偏移，增大范围

      baseOffsetX += offsetVariationX;
      baseOffsetY = offsetVariationY;

      console.log(`🔄 应用避让偏移 ${displayName}: (${baseOffsetX}, ${baseOffsetY})`);
    }

    // 根据名称长度微调偏移
    const nameLength = displayName.length;
    if (nameLength > 15) {
      baseOffsetY -= 10; // 长名称稍微向上偏移
    }

    return new Cesium.Cartesian2(baseOffsetX, baseOffsetY);
  }

  // 检查是否需要标签避让 - 更全面的检查
  private needsLabelAvoidance(position: { longitude: number; latitude: number; altitude: number }): boolean {
    // 首先检查是否在已知的密集区域
    if (this.isInDenseRegion(position)) {
      return true;
    }

    // 检查是否有其他发射场在附近（更大的范围）
    const nearbyThreshold = 3.0; // 3.0度范围内，扩大检测范围
    let nearbyCount = 0;

    this.launchSites.forEach(site => {
      const distance = Math.sqrt(
        Math.pow(site.position.longitude - position.longitude, 2) +
        Math.pow(site.position.latitude - position.latitude, 2)
      );

      if (distance < nearbyThreshold && distance > 0) {
        nearbyCount++;
      }
    });

    // 如果附近有1个或更多发射场，就需要避让 - 降低阈值
    const needsAvoidance = nearbyCount >= 1;

    if (needsAvoidance) {
      console.log(`🔍 发射场需要避让: 位置(${position.longitude.toFixed(2)}, ${position.latitude.toFixed(2)}) 附近有 ${nearbyCount} 个发射场`);
    }

    return needsAvoidance;
  }

  // 检查是否在发射场密集区域
  private isInDenseRegion(position: { longitude: number; latitude: number; altitude: number }): boolean {
    const { longitude, latitude } = position;

    // 定义发射场密集区域
    const denseRegions = [
      // 美国佛罗里达州（肯尼迪航天中心、卡纳维拉尔角等）
      { minLon: -81.5, maxLon: -80.0, minLat: 28.0, maxLat: 29.0 },
      // 哈萨克斯坦拜科努尔地区
      { minLon: 63.0, maxLon: 64.0, minLat: 45.5, maxLat: 46.5 },
      // 中国海南文昌地区
      { minLon: 110.5, maxLon: 111.0, minLat: 19.5, maxLat: 20.0 },
      // 中国酒泉地区
      { minLon: 99.5, maxLon: 100.5, minLat: 40.5, maxLat: 41.5 },
      // 俄罗斯普列谢茨克地区
      { minLon: 40.0, maxLon: 41.0, minLat: 62.5, maxLat: 63.5 },
      // 法属圭亚那库鲁地区
      { minLon: -53.0, maxLon: -52.0, minLat: 5.0, maxLat: 6.0 }
    ];

    return denseRegions.some(region =>
      longitude >= region.minLon && longitude <= region.maxLon &&
      latitude >= region.minLat && latitude <= region.maxLat
    );
  }

  // 清理发射场名称，确保显示效果良好
  private cleanLaunchSiteName(name: string): string {
    if (!name) return '未知发射场';

    // 移除多余的空格
    let cleanName = name.trim();

    // 处理特殊字符和声调符号
    cleanName = this.normalizeSpecialCharacters(cleanName);

    // 检查是否包含明显的字符混乱模式
    if (this.hasGarbledText(cleanName)) {
      // 尝试修复常见的字符混乱
      cleanName = this.fixGarbledText(cleanName);
    }

    // 更精确地移除重复的字符或词汇 - 只处理明显的重复模式
    // 移除连续重复的相同字符（3个或更多）
    cleanName = cleanName.replace(/(.)\1{2,}/g, '$1');

    // 移除重复的单词（用空格分隔的）- 改进版本
    const words = cleanName.split(/\s+/);
    const uniqueWords = [];
    const seenWords = new Set();

    for (const word of words) {
      const lowerWord = word.toLowerCase();
      // 跳过已经见过的单词（忽略大小写）
      if (!seenWords.has(lowerWord) && word.length > 0) {
        uniqueWords.push(word);
        seenWords.add(lowerWord);
      }
    }
    cleanName = uniqueWords.join(' ');

    // 特殊处理：移除明显的重复模式
    // 例如："Wakayama Prefecture 纪伊航天大港" 中的重复
    cleanName = cleanName.replace(/^([A-Za-z\s]+)\s+([^\s]+.*?)$/g, (match, _englishPart, chinesePart) => {
      // 如果有英文部分和中文部分，优先保留中文部分
      if (/[\u4e00-\u9fff]/.test(chinesePart)) {
        return chinesePart.trim();
      }
      return match;
    });

    // 进一步处理重复的英文和中文混合
    // 例如："Tanegashima Space Center 种子岛航天中心" -> "种子岛航天中心"
    cleanName = cleanName.replace(/^([A-Za-z\s]+)\s+([\u4e00-\u9fff]+.*?)$/g, (_match, _englishPart, chinesePart) => {
      return chinesePart.trim();
    });

    // 移除连续的大小写混合乱码
    cleanName = cleanName.replace(/[A-Z][a-z]*[A-Z][a-z]*[A-Z][a-z]*/g, (match) => {
      // 如果是明显的乱码模式，尝试提取有意义的部分
      if (match.length > 10 && /[A-Z]{2,}/.test(match)) {
        return match.split(/(?=[A-Z])/)[0] || 'Station';
      }
      return match;
    });

    // 清理多余的空格
    cleanName = cleanName.replace(/\s+/g, ' ').trim();

    // 限制名称长度
    if (cleanName.length > 25) {
      cleanName = cleanName.substring(0, 22) + '...';
    }

    // 最终检查：如果清理后的名称仍然看起来有问题，使用简化名称
    if (this.isStillGarbled(cleanName)) {
      // 尝试提取前面有意义的部分
      const meaningfulPart = cleanName.match(/^[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*/);
      if (meaningfulPart) {
        cleanName = meaningfulPart[0];
      } else {
        cleanName = '发射场';
      }
    }

    console.log(`🧹 名称清理: "${name}" → "${cleanName}"`);
    return cleanName || '发射场';
  }

  // 标准化特殊字符，处理声调符号等
  private normalizeSpecialCharacters(text: string): string {
    // 声调符号映射表
    const accentMap: { [key: string]: string } = {
      // 拉丁字母带声调符号
      'à': 'a', 'á': 'a', 'â': 'a', 'ã': 'a', 'ä': 'a', 'å': 'a', 'ā': 'a', 'ă': 'a', 'ą': 'a',
      'è': 'e', 'é': 'e', 'ê': 'e', 'ë': 'e', 'ē': 'e', 'ĕ': 'e', 'ė': 'e', 'ę': 'e', 'ě': 'e',
      'ì': 'i', 'í': 'i', 'î': 'i', 'ï': 'i', 'ī': 'i', 'ĭ': 'i', 'į': 'i', 'ı': 'i',
      'ò': 'o', 'ó': 'o', 'ô': 'o', 'õ': 'o', 'ö': 'o', 'ø': 'o', 'ō': 'o', 'ŏ': 'o', 'ő': 'o',
      'ù': 'u', 'ú': 'u', 'û': 'u', 'ü': 'u', 'ū': 'u', 'ŭ': 'u', 'ů': 'u', 'ű': 'u', 'ų': 'u',
      'ý': 'y', 'ÿ': 'y', 'ŷ': 'y',
      'ñ': 'n', 'ń': 'n', 'ň': 'n', 'ņ': 'n',
      'ç': 'c', 'ć': 'c', 'ĉ': 'c', 'ċ': 'c', 'č': 'c',
      'ś': 's', 'ŝ': 's', 'ş': 's', 'š': 's',
      'ź': 'z', 'ż': 'z', 'ž': 'z',
      'ł': 'l', 'ĺ': 'l', 'ļ': 'l', 'ľ': 'l',
      'ř': 'r', 'ŕ': 'r', 'ŗ': 'r',
      'ğ': 'g', 'ĝ': 'g', 'ġ': 'g', 'ģ': 'g',
      'ĥ': 'h', 'ħ': 'h',
      'ĵ': 'j',
      'ķ': 'k',
      'ţ': 't', 'ť': 't', 'ŧ': 't',
      'ŵ': 'w',
      // 大写字母
      'À': 'A', 'Á': 'A', 'Â': 'A', 'Ã': 'A', 'Ä': 'A', 'Å': 'A', 'Ā': 'A', 'Ă': 'A', 'Ą': 'A',
      'È': 'E', 'É': 'E', 'Ê': 'E', 'Ë': 'E', 'Ē': 'E', 'Ĕ': 'E', 'Ė': 'E', 'Ę': 'E', 'Ě': 'E',
      'Ì': 'I', 'Í': 'I', 'Î': 'I', 'Ï': 'I', 'Ī': 'I', 'Ĭ': 'I', 'Į': 'I',
      'Ò': 'O', 'Ó': 'O', 'Ô': 'O', 'Õ': 'O', 'Ö': 'O', 'Ø': 'O', 'Ō': 'O', 'Ŏ': 'O', 'Ő': 'O',
      'Ù': 'U', 'Ú': 'U', 'Û': 'U', 'Ü': 'U', 'Ū': 'U', 'Ŭ': 'U', 'Ů': 'U', 'Ű': 'U', 'Ų': 'U',
      'Ý': 'Y', 'Ÿ': 'Y', 'Ŷ': 'Y',
      'Ñ': 'N', 'Ń': 'N', 'Ň': 'N', 'Ņ': 'N',
      'Ç': 'C', 'Ć': 'C', 'Ĉ': 'C', 'Ċ': 'C', 'Č': 'C',
      'Ś': 'S', 'Ŝ': 'S', 'Ş': 'S', 'Š': 'S',
      'Ź': 'Z', 'Ż': 'Z', 'Ž': 'Z',
      'Ł': 'L', 'Ĺ': 'L', 'Ļ': 'L', 'Ľ': 'L',
      'Ř': 'R', 'Ŕ': 'R', 'Ŗ': 'R',
      'Ğ': 'G', 'Ĝ': 'G', 'Ġ': 'G', 'Ģ': 'G',
      'Ĥ': 'H', 'Ħ': 'H',
      'Ĵ': 'J',
      'Ķ': 'K',
      'Ţ': 'T', 'Ť': 'T', 'Ŧ': 'T',
      'Ŵ': 'W'
    };
    
    // 替换所有带声调的字符
    let normalized = text;
    Object.entries(accentMap).forEach(([accented, normal]) => {
      normalized = normalized.replace(new RegExp(accented, 'g'), normal);
    });
    
    return normalized;
  }

  // 检测是否包含明显的字符混乱
  private hasGarbledText(text: string): boolean {
    // 检测连续的大小写混合且长度异常的字符串
    const garbledPattern = /[A-Z][a-z]*[A-Z][a-z]*[A-Z][a-z]*[A-Z]/;
    const hasGarbled = garbledPattern.test(text);
    
    // 检测是否包含明显的重复字符模式
    const hasRepeats = /(.{3,})\1/.test(text);
    
    return hasGarbled || hasRepeats;
  }

  // 修复常见的字符混乱
  private fixGarbledText(text: string): string {
    let fixed = text;
    
    // 检查是否是已知的发射场名称，如果是则直接使用标准名称
    const knownLaunchSites: { [key: string]: string } = {
      'tanegashima': '种子岛航天中心',
      'baikonur': '拜科努尔航天发射场',
      'kennedy': '肯尼迪航天中心',
      'vandenberg': '范登堡空军基地',
      'plesetsk': '普列谢茨克航天发射场',
      'jiuquan': '酒泉卫星发射中心',
      'xichang': '西昌卫星发射中心',
      'taiyuan': '太原卫星发射中心',
      'wenchang': '文昌航天发射场',
      'kourou': '库鲁航天中心',
      'wallops': '瓦洛普斯飞行研究所',
      'cape': '卡纳维拉尔角',
      'edwards': '爱德华兹空军基地'
    };
    
    // 检查是否包含已知发射场名称
    const lowerText = text.toLowerCase();
    for (const [key, standardName] of Object.entries(knownLaunchSites)) {
      if (lowerText.includes(key)) {
        return standardName;
      }
    }
    
    // 如果包含明显的混合词汇，尝试分离
    if (this.hasOverlappingWords(text)) {
      fixed = this.separateOverlappingWords(text);
    }
    
    // 修复常见的字符混乱模式
    const fixPatterns: { [key: string]: string } = {
      'RBeketchLauncher': 'Rocket Launch',
      'RBerearRuler': 'Rocket',
      'Sketchlons': 'Launch',
      'Thinazwa': 'Launch',
      'Ketchlons': 'Launch',
      'Berear': 'Launch',
      'Ruler': 'Site',
      'elsland': 'Island',
      'maelsland': 'Island'
    };
    
    // 应用修复模式
    Object.entries(fixPatterns).forEach(([garbled, correct]) => {
      fixed = fixed.replace(new RegExp(garbled, 'gi'), correct);
    });
    
    // 移除明显的乱码字符序列（连续的大小写混合）
    fixed = fixed.replace(/[A-Z][a-z]*[A-Z][a-z]*[A-Z][a-z]*(?=[A-Z])/g, '');
    
    // 清理多余的空格
    fixed = fixed.replace(/\s+/g, ' ').trim();
    
    return fixed;
  }

  // 检测是否有重叠的词汇
  private hasOverlappingWords(text: string): boolean {
    // 检测明显的词汇重叠模式
    const overlappingPatterns = [
      /[a-z]+[A-Z][a-z]+[a-z]+/,  // 如：tanegashimaIsland
      /[A-Z][a-z]+[a-z]+[A-Z]/,   // 如：TanegashimaIsland
      /[a-z]{6,}[a-z]{6,}/,       // 连续长单词
    ];
    
    return overlappingPatterns.some(pattern => pattern.test(text));
  }

  // 分离重叠的词汇
  private separateOverlappingWords(text: string): string {
    // 尝试在大写字母前分割
    let separated = text.replace(/([a-z])([A-Z])/g, '$1 $2');
    
    // 如果仍然很长，尝试其他分割方法
    if (separated.length > 15) {
      // 尝试识别常见的后缀并分割
      separated = separated.replace(/(island|center|site|base|station|pad)([a-z])/gi, '$1 $2');
      separated = separated.replace(/([a-z])(island|center|site|base|station|pad)/gi, '$1 $2');
    }
    
    return separated;
  }

  // 检查清理后的文本是否仍然有问题
  private isStillGarbled(text: string): boolean {
    // 如果包含超过3个连续的大写字母（除了已知的缩写）
    if (/[A-Z]{4,}/.test(text) && !/NASA|ISRO|ESA|JAXA/.test(text)) {
      return true;
    }

    // 如果包含明显的无意义字符组合
    if (/[bcdfghjklmnpqrstvwxyz]{4,}/i.test(text)) {
      return true;
    }

    return false;
  }

  // 检查两个标签是否重叠
  private labelsOverlap(
    label1: {x: number, y: number, width: number, height: number},
    label2: {x: number, y: number, width: number, height: number},
    margin: number = 10
  ): boolean {
    // 计算每个标签的边界（加上边距）
    const l1Left = label1.x - label1.width / 2 - margin;
    const l1Right = label1.x + label1.width / 2 + margin;
    const l1Top = label1.y - label1.height / 2 - margin;
    const l1Bottom = label1.y + label1.height / 2 + margin;

    const l2Left = label2.x - label2.width / 2 - margin;
    const l2Right = label2.x + label2.width / 2 + margin;
    const l2Top = label2.y - label2.height / 2 - margin;
    const l2Bottom = label2.y + label2.height / 2 + margin;

    // 检查是否重叠
    const noOverlap = l1Right < l2Left || l2Right < l1Left || l1Bottom < l2Top || l2Bottom < l1Top;

    return !noOverlap;
  }

  // 创建现代化的发射场图标
  private createLaunchSiteIcon(): string {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const context = canvas.getContext('2d')!;

    // 启用抗锯齿
    context.imageSmoothingEnabled = true;
    context.imageSmoothingQuality = 'high';

    // 绘制外圈发光效果
    const glowGradient = context.createRadialGradient(32, 32, 0, 32, 32, 32);
    glowGradient.addColorStop(0, 'rgba(255, 165, 0, 0.4)'); // 橙色发光
    glowGradient.addColorStop(0.7, 'rgba(255, 165, 0, 0.2)');
    glowGradient.addColorStop(1, 'rgba(255, 165, 0, 0)');

    context.fillStyle = glowGradient;
    context.fillRect(0, 0, 64, 64);

    // 绘制主体火箭图标 - 更大更清晰的设计
    context.save();
    context.translate(32, 32);

    // 火箭主体 - 更大的尺寸
    context.beginPath();
    context.moveTo(0, -24);  // 顶部尖端
    context.lineTo(-6, -14); // 左侧
    context.lineTo(-6, 10);  // 左侧底部
    context.lineTo(-10, 16); // 左侧尾翼
    context.lineTo(-3, 20);  // 左侧尾翼内侧
    context.lineTo(0, 18);   // 底部中心
    context.lineTo(3, 20);   // 右侧尾翼内侧
    context.lineTo(10, 16);  // 右侧尾翼
    context.lineTo(6, 10);   // 右侧底部
    context.lineTo(6, -14);  // 右侧
    context.closePath();

    // 主体渐变填充 - 更鲜明的颜色
    const bodyGradient = context.createLinearGradient(0, -24, 0, 20);
    bodyGradient.addColorStop(0, '#ffffff');
    bodyGradient.addColorStop(0.2, '#ffa940'); // 橙色
    bodyGradient.addColorStop(0.6, '#fa8c16'); // 深橙色
    bodyGradient.addColorStop(1, '#d46b08');   // 更深的橙色
    context.fillStyle = bodyGradient;
    context.fill();

    // 主体描边 - 更粗的白色描边
    context.strokeStyle = '#ffffff';
    context.lineWidth = 2.5;
    context.stroke();

    // 绘制火箭窗口
    context.beginPath();
    context.arc(0, -10, 3, 0, 2 * Math.PI);
    context.fillStyle = '#69c0ff';
    context.fill();
    context.strokeStyle = '#ffffff';
    context.lineWidth = 1.2;
    context.stroke();

    // 绘制火箭尾焰效果 - 更大更明显
    context.beginPath();
    context.moveTo(-3, 18);
    context.lineTo(0, 26);
    context.lineTo(3, 18);
    context.closePath();

    const flameGradient = context.createLinearGradient(0, 18, 0, 26);
    flameGradient.addColorStop(0, '#ff7875');
    flameGradient.addColorStop(0.5, '#ff4d4f');
    flameGradient.addColorStop(1, 'rgba(255, 77, 79, 0.4)');
    context.fillStyle = flameGradient;
    context.fill();

    // 添加额外的发光边缘
    context.strokeStyle = 'rgba(255, 165, 0, 0.8)';
    context.lineWidth = 1;
    context.stroke();

    context.restore();

    return canvas.toDataURL();
  }

  // 修改事件处理器，添加发射场点击处理和鼠标悬停功能
  private setupEventHandlers() {
    const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this.mouseHandler = handler; // 保存引用以便销毁时清理
    
    // 🚫 暂时注释掉单击事件处理，避免与SatelliteController的单击事件冲突
    // 单击事件处理
    // handler.setInputAction((click: any) => {
    //   const pickedObject = this.viewer.scene.pick(click.position);
    //   if (pickedObject && pickedObject.id) {
    //     const entity = pickedObject.id;
    //     const id = entity.id;
        
    //     // 检查是否是发射场实体
    //     if (id.startsWith('launch-site-')) {
    //       const siteId = id.replace('launch-site-', '');
    //       const site = this.launchSites.get(siteId);
    //       if (site && this.onLaunchSiteClick) {
    //         this.onLaunchSiteClick(site);
    //       }
    //     }
    //   }
    // }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    
    // 双击事件处理 - 支持轻量级卫星点升级
    handler.setInputAction((click: any) => {
      const pickedObject = this.viewer.scene.pick(click.position);
      if (pickedObject) {
        // 检查是否是轻量级卫星点
        if (pickedObject.primitive === this.lightSatelliteRenderer.pointCollection) {
          const pointPrimitive = pickedObject.id;
          if (pointPrimitive && typeof pointPrimitive === 'string') {
            console.log(`双击轻量级卫星点: ${pointPrimitive}`);
            // 升级为完整Entity
            const success = this.lightSatelliteRenderer.promoteToEntity(pointPrimitive);
            if (success) {
              console.log(`成功将卫星 ${pointPrimitive} 升级为Entity`);
            }
          }
        }
        // 检查是否是Entity
        else if (pickedObject.id) {
          const entity = pickedObject.id;
          const id = entity.id;
          
          // 处理其他Entity的双击事件
          console.log(`双击Entity: ${id}`);
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);

    // 鼠标移动事件处理 - 实现悬停功能
    handler.setInputAction((movement: any) => {
      this.handleMouseMove(movement.endPosition);
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  }

  /**
   * 定位空间物体（卫星、碎片、空间站）
   */
  async locateSpaceObject(id: string, type: string, tle: { tle1: string; tle2: string }) {
    console.log('Locating space object:', { id, type, tle });
    try {
      switch (type) {
        case 'satellite': {
          const satellite = await this.satelliteService.getSatelliteById(id);
          if (satellite) {
            // 清理现有的轨道实体
            this.satelliteController.clearOrbitEntities();
            // 更新卫星的TLE数据
            satellite.orbitInfo.tle = [tle.tle1, tle.tle2];
            // 添加卫星和轨道
            this.satelliteController.addSatellite(satellite);
            // 跟踪卫星
            this.satelliteController.trackSatellite(satellite.id);
          } else {
            console.error('Satellite not found or missing TLE data');
          }
          break;
        }
        case 'debris': {
          const debris = await this.debrisService.getDebrisById(id);
          if (debris) {
            // 清理现有的轨道实体
            this.satelliteController.clearOrbitEntities();
            // 更新碎片的TLE数据
            debris.orbitInfo.tle = [tle.tle1, tle.tle2];
            // 添加碎片和轨道
            this.satelliteController.addDebris(debris);
            // 跟踪碎片
            const debrisEntity = this.satelliteController.getDebrisEntity(debris.id);
            if (debrisEntity) {
              this.viewer.trackedEntity = debrisEntity;
            }
          } else {
            console.error('Debris not found or missing TLE data');
          }
          break;
        }
        case 'spacestation': {
          const spaceStation = await this.satelliteService.getSatelliteById(id);
          if (spaceStation) {
            // 清理现有的轨道实体
            this.satelliteController.clearOrbitEntities();
            // 更新空间站的TLE数据
            spaceStation.orbitInfo.tle = [tle.tle1, tle.tle2];
            // 添加空间站和轨道
            this.satelliteController.addSatellite(spaceStation);
            // 跟踪空间站
            this.satelliteController.trackSatellite(spaceStation.id);
          } else {
            console.error('Space station not found or missing TLE data');
          }
          break;
        }
        default:
          console.error('Unknown space object type:', type);
      }
    } catch (error) {
      console.error('Error locating space object:', error);
    }
  }

  /**
   * 定位地面物体（发射场、地面站）
   */
  async locateGroundObject(params: GroundObjectParams) {
    const { id, type, position } = params;
    
    try {
      switch (type) {
        case 'launchsite':
          // 获取发射场数据
          const launchSite = await this.launchSiteService.getLaunchSiteById(id);
          if (launchSite) {
            // 如果提供了新的位置信息，更新发射场位置
            if (position) {
              launchSite.position = position;
            }
            // 显示发射场
            await this.showLaunchSites();
            // 设置相机位置
            this.flyToPosition(launchSite.position);
          }
          break;
        
        case 'groundstation':
          // 获取地面站数据
          const groundStation = await this.groundStationService.getGroundStationById(id);
          if (groundStation) {
            // 如果提供了新的位置信息，更新地面站位置
            if (position) {
              groundStation.position = position;
            }
            // 显示地面站
            await this.showGroundStation(groundStation);
            // 设置相机位置
            this.flyToPosition(groundStation.position);
          }
          break;
      }
    } catch (error) {
      console.error('Error locating ground object:', error);
    }
  }

  /**
   * 飞行到指定位置
   */
  private flyToPosition(position: { longitude: number; latitude: number; altitude: number }) {
    const destination = Cesium.Cartesian3.fromDegrees(
      position.longitude,
      position.latitude,
      position.altitude + 10000 // 在物体上方10km处观察
    );
    
    this.viewer.camera.flyTo({
      destination,
      orientation: {
        heading: 0.0,
        pitch: -0.5,
        roll: 0.0
      }
    });
  }

  /**
   * 显示地面站
   */
  private async showGroundStation(groundStation: any) {
    // 创建地面站实体
    const entity = new Cesium.Entity({
      id: groundStation.id,
      name: groundStation.name,
      position: Cesium.Cartesian3.fromDegrees(
        groundStation.position.longitude,
        groundStation.position.latitude,
        groundStation.position.altitude
      ),
      billboard: {
        image: '/icons/ground-station.svg',
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        scale: 1.0,
        disableDepthTestDistance: undefined
      },
      label: {
        text: groundStation.name,
        font: '14px sans-serif',
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        pixelOffset: new Cesium.Cartesian2(0, -32),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        disableDepthTestDistance: undefined
      }
    });

    // 添加到场景
    this.viewer.entities.add(entity);
  }

  /**
   * 切换地球自转状态
   * @param enable 是否启用地球自转，如果不提供则切换当前状态
   * @returns 切换后的地球自转状态
   */
  public toggleEarthRotation(enable?: boolean): boolean {
    // 如果提供了参数，则直接设置状态，否则切换当前状态
    this.earthRotationEnabled = enable !== undefined ? enable : !this.earthRotationEnabled;
    
    // 更新当前时间以防止大幅跳变
    if (this.clock) {
      this.lastRotationTime = Cesium.JulianDate.toDate(this.clock.currentTime);
    }
    
    return this.earthRotationEnabled;
  }

  /**
   * 获取当前地球自转状态
   * @returns 地球自转是否启用
   */
  public isEarthRotationEnabled(): boolean {
    return this.earthRotationEnabled;
  }

  /**
   * 设置时钟监听器
   */
  private setupClockListener(): void {
    // 移除之前的监听器（如果存在）
    if (this.clockTickListener) {
      this.clockTickListener();
      // console.log('移除之前的时钟监听器'); // 注释掉频繁的日志
    }
    
    // 🌟 添加实时更新控制变量
    let updateErrorCount = 0;
    const MAX_ERROR_COUNT = 5; // 最大错误次数
    let isRealtimeUpdateEnabled = true;
    
    // 添加新的监听器
    this.clockTickListener = this.viewer.clock.onTick.addEventListener((clock: Cesium.Clock) => {
      // 获取当前时间
      const currentTime = Cesium.JulianDate.toDate(clock.currentTime);
      
      // 🌟 安全的实时更新功能
      if (isRealtimeUpdateEnabled && this.lightSatelliteRenderer && clock.shouldAnimate) {
        try {
          // 添加基础检查
          if (!clock.currentTime || !this.lightSatelliteRenderer.getSatelliteCount()) {
            return;
          }
          
          // 限制更新频率 - 每5帧更新一次，减少计算负担
          const frameNumber = this.viewer.scene.frameState.frameNumber;
          if (frameNumber % 5 !== 0) {
            return;
          }
          
          this.lightSatelliteRenderer.updatePositionsRealtime(clock.currentTime);
          
          // 如果成功执行，重置错误计数
          if (updateErrorCount > 0) {
            updateErrorCount = Math.max(0, updateErrorCount - 1);
          }
          
        } catch (error) {
          updateErrorCount++;
          console.warn(`实时位置更新出错 (${updateErrorCount}/${MAX_ERROR_COUNT}):`, error);
          
          // 如果错误次数超过阈值，暂时禁用实时更新
          if (updateErrorCount >= MAX_ERROR_COUNT) {
            isRealtimeUpdateEnabled = false;
            console.error('实时位置更新错误次数过多，已禁用。将在30秒后重新尝试启用。');
            
            // 30秒后重新尝试启用
            setTimeout(() => {
              updateErrorCount = 0;
              isRealtimeUpdateEnabled = true;
              console.log('重新启用实时位置更新功能');
            }, 30000);
          }
        }
      }
      
      // 检查是否需要更新卫星位置（针对Entity模式的卫星）
      this.checkAndUpdateSatellitePositions(currentTime);
    });
    
    console.log('成功设置时钟监听器，将实时更新卫星轨道显示');
  }

  /**
   * 检查并更新卫星位置
   */
  private checkAndUpdateSatellitePositions(currentTime: Date): void {
    // 如果时钟没有运行，则不进行更新
    if (!this.viewer.clock.shouldAnimate) {
      return;
    }
    
    // 创建安全处理的卫星ID列表，防止在迭代过程中修改原始Map
    const satelliteIds = Array.from(this.satelliteDataCache.keys());
    
    // 循环检查每个卫星是否需要更新位置
    for (const satId of satelliteIds) {
      // 首先检查缓存中是否仍存在此卫星
      const cacheInfo = this.satelliteDataCache.get(satId);
      if (!cacheInfo) {
        // 卫星已从缓存中移除，清理相关数据
        this.lastOrbitUpdateTimes.delete(satId);
        console.log(`卫星 ${satId} 已不在缓存中，跳过更新`);
        continue;
      }
      
      // 如果已经在计算中，则跳过
      if (cacheInfo.calculating) {
        continue;
      }
      
      // 获取卫星实体
      const entity = this.satelliteController.getSatelliteEntity(satId);
      if (!entity || !entity.position) {
        // 实体不存在或已被删除，清理缓存数据
        console.log(`卫星 ${satId} 的实体不存在或已被删除，清理相关缓存数据`);
        this.satelliteDataCache.delete(satId);
        this.lastOrbitUpdateTimes.delete(satId);
        continue;
      }
      
      // 获取卫星数据
      const trackedSatellite = this.satelliteController.getTrackedSatellite(satId);
      if (!trackedSatellite || !trackedSatellite.line1 || !trackedSatellite.line2) {
        // 跟踪数据不存在或已被删除，清理缓存数据
        console.log(`卫星 ${satId} 的跟踪数据不存在或已被删除，清理相关缓存数据`);
        this.satelliteDataCache.delete(satId);
        this.lastOrbitUpdateTimes.delete(satId);
        continue;
      }
      
      // 计算轨道周期（分钟）
      let orbitalPeriod = 90; // 默认90分钟
      try {
        // 从TLE第二行解析平均运动
        const tle2 = trackedSatellite.line2;
        const meanMotionStr = tle2.substring(52, 63).trim();
        const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
        if (!isNaN(meanMotion) && meanMotion > 0) {
          orbitalPeriod = (24 * 60) / meanMotion;
        }
      } catch (error) {
        console.warn(`计算卫星轨道周期失败:`, error);
      }
      
      // 关键逻辑改进：增加更多触发轨道更新的条件
      const lastUpdateTime = this.lastOrbitUpdateTimes.get(satId) || new Date(0);
      const elapsedMinutesSinceLastUpdate = (currentTime.getTime() - lastUpdateTime.getTime()) / 60000;
      
      // 计算距离数据边界的时间（分钟）
      const timeToDataEndMs = cacheInfo.nextCalculationTime.getTime() - currentTime.getTime();
      const minutesToDataEnd = timeToDataEndMs / 60000;
      
      // 确定是否需要更新轨道的条件
      // 1. 走完1/4轨道周期
      // 2. 首次更新
      // 3. 接近数据边界（不到一个轨道周期）
      // 4. 每小时至少更新一次
      const needsOrbitUpdate = 
        elapsedMinutesSinceLastUpdate >= orbitalPeriod / 4 || 
        lastUpdateTime.getTime() === 0 ||
        minutesToDataEnd < orbitalPeriod ||
        elapsedMinutesSinceLastUpdate >= 60;
      
      // 如果需要更新轨道
      if (needsOrbitUpdate && entity.path) {
        const updateReason = 
          elapsedMinutesSinceLastUpdate >= orbitalPeriod / 4 ? '已走完1/4轨道周期' : 
          lastUpdateTime.getTime() === 0 ? '首次更新' :
          minutesToDataEnd < orbitalPeriod ? '接近数据边界' : '每小时定期更新';
        
        console.log(`更新卫星 ${satId} 的轨道线，原因: ${updateReason}, 距上次更新已过去 ${elapsedMinutesSinceLastUpdate.toFixed(2)} 分钟`);
        
        // 计算轨道周期（秒）
        const periodInSeconds = orbitalPeriod * 60;
        
        // 修改轨道线前后显示范围
        // 将轨道线显示为卫星未来一个完整轨道周期的轨迹
        entity.path.leadTime = periodInSeconds; // 显示未来一个完整轨道周期
        entity.path.trailTime = 1; // 设为1秒，几乎不显示历史轨迹
        
        // 确保轨道线可见
        entity.path.show = true;
        
        // 记录更新时间
        this.lastOrbitUpdateTimes.set(satId, new Date(currentTime));
        
        // 如果接近数据边界，提前计算新的位置数据
        if (minutesToDataEnd < orbitalPeriod && !cacheInfo.calculating) {
          console.log(`卫星 ${satId} 接近数据边界，距离数据结束还有 ${minutesToDataEnd.toFixed(1)} 分钟，提前计算新的位置数据`);
          
          // 标记为正在计算
          cacheInfo.calculating = true;
          
          // 使用TIME_WINDOW_HOURS小时的窗口计算新的位置数据
          const newStartTime = new Date(currentTime);
          const newEndTime = new Date(currentTime.getTime() + (this.TIME_WINDOW_HOURS || 12) * 60 * 60 * 1000);
          
          // 异步计算并添加新的位置点
          this.calculateAndAddSatellitePositions(
            satId, 
            trackedSatellite.name || satId, 
            trackedSatellite.line1, 
            trackedSatellite.line2, 
            newStartTime, 
            newEndTime,
            30 // 使用较大的采样间隔以提高性能
          ).then(() => {
            // 更新缓存信息
            cacheInfo.lastCalculationTime = newStartTime;
            cacheInfo.nextCalculationTime = newEndTime;
            cacheInfo.calculating = false;
            
            console.log(`卫星 ${satId} 位置数据已更新至 ${newEndTime.toISOString()}`);
          }).catch(error => {
            console.error(`计算卫星 ${satId} 位置时出错:`, error);
            cacheInfo.calculating = false;
          });
        }
        
        // 强制场景更新
        this.viewer.scene.requestRender();
      }
      
      // 保留原有的位置数据计算逻辑作为备份机制
      // 计算当前时间到下一计算时间的时间差（分钟）
      const timeToNextCalculation = (cacheInfo.nextCalculationTime.getTime() - currentTime.getTime()) / 60000;
      
      // 如果时间差小于阈值，则触发新的计算
      if (timeToNextCalculation < this.RECALCULATION_THRESHOLD_MINUTES && !cacheInfo.calculating) {
        console.log(`备份机制：卫星 ${satId} 接近计算范围末尾，剩余 ${timeToNextCalculation.toFixed(1)} 分钟，开始计算下一时间段`);
        
        // 标记为正在计算
        cacheInfo.calculating = true;
        
        // 获取TLE数据
        if (trackedSatellite && trackedSatellite.line1 && trackedSatellite.line2) {
          // 设置计算的起始时间为当前计算的结束时间
          const startTime = new Date(cacheInfo.nextCalculationTime);
          // 设置计算的结束时间为起始时间加上预加载窗口（扩大到TIME_WINDOW_HOURS小时）
          const windowSizeMs = (this.TIME_WINDOW_HOURS || 12) * 60 * 60 * 1000;
          const endTime = new Date(startTime.getTime() + windowSizeMs);
          
          // 异步计算并添加新的位置点
          this.calculateAndAddSatellitePositions(
            satId, 
            trackedSatellite.name || satId, 
            trackedSatellite.line1, 
            trackedSatellite.line2, 
            startTime, 
            endTime
          ).then(() => {
            // 更新缓存信息
            cacheInfo.lastCalculationTime = startTime;
            cacheInfo.nextCalculationTime = endTime;
            cacheInfo.calculating = false;
            
            // 重置轨道更新时间，强制下次检查时更新轨道
            this.lastOrbitUpdateTimes.set(satId, new Date(0));
            
            console.log(`卫星 ${satId} 位置数据已更新至 ${endTime.toISOString()}，将在下次检查时更新轨道`);
          }).catch(error => {
            console.error(`计算卫星 ${satId} 位置时出错:`, error);
            cacheInfo.calculating = false;
          });
        } else {
          console.warn(`卫星 ${satId} 缺少TLE数据，无法计算新的位置`);
          cacheInfo.calculating = false;
        }
      }
    }
  }

  /**
   * 添加一个卫星到场景并初始化其位置
   * @param satelliteId 卫星ID
   * @param satName 卫星名称 
   * @param tleLine1 TLE第一行
   * @param tleLine2 TLE第二行
   */
  public async addAndTrackSatellite(
    satelliteId: string, 
    satName: string, 
    tleLine1: string, 
    tleLine2: string
  ): Promise<void> {
    try {
      console.log(`添加并跟踪卫星: ID=${satelliteId}, 名称=${satName}`);
      console.log(`TLE数据: 
      - 第一行: ${tleLine1}
      - 第二行: ${tleLine2}`);
      
      // 首先让SatelliteController跟踪并创建实体
      this.satelliteController.trackSatellite(satelliteId, satName, {
        line1: tleLine1,
        line2: tleLine2
      });
      
      // 获取当前时间
      const currentTime = Cesium.JulianDate.toDate(this.viewer.clock.currentTime);
      
      // 设置初始计算时间范围
      const startTime = new Date(currentTime);
      startTime.setMinutes(startTime.getMinutes() - 5); // 从5分钟前开始
      
      const endTime = new Date(currentTime);
      endTime.setMinutes(endTime.getMinutes() + this.PRELOAD_WINDOW_MINUTES);
      
      // 计算初始轨道点
      await this.calculateAndAddSatellitePositions(
        satelliteId,
        satName,
        tleLine1,
        tleLine2,
        startTime,
        endTime
      );
      
      // 初始化缓存信息
      this.satelliteDataCache.set(satelliteId, {
        lastCalculationTime: startTime,
        nextCalculationTime: endTime,
        calculating: false
      });
      
      // 启用自动位置更新
      this.setupClockListener();
      
      console.log(`卫星 ${satelliteId} (${satName}) 已添加并开始跟踪`);
      
      // 强制场景更新
      this.viewer.scene.requestRender();
      
    } catch (error) {
      console.error(`添加卫星 ${satelliteId} 时出错:`, error);
    }
  }

  /**
   * 计算并添加卫星位置
   * @param satId 卫星ID
   * @param satName 卫星名称
   * @param line1 TLE第一行
   * @param line2 TLE第二行
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param sampleInterval 采样间隔（秒）
   */
  private async calculateAndAddSatellitePositions(
    satId: string, 
    satName: string, 
    line1: string, 
    line2: string, 
    startTime: Date, 
    endTime: Date,
    sampleInterval: number = 180 // 从60秒增加到180秒，降低采样率提高性能
  ): Promise<void> {
    try {
      console.log(`开始计算卫星 ${satId} (${satName}) 的位置...`);
      console.log(`原始时间范围: ${startTime.toISOString()} 到 ${endTime.toISOString()}`);
      
      // 确保TLE格式正确
      const processedLine1 = this.ensureTLEFormat(line1, 1);
      const processedLine2 = this.ensureTLEFormat(line2, 2);
      
      // 验证TLE数据
      if (!processedLine1 || !processedLine2) {
        console.error(`卫星 ${satId} 的TLE数据无效`);
            return;
          }
          
      // 获取实体对象，如果不存在则创建
      const entity = this.satelliteController.getSatelliteEntity(satId) || 
                    this.satelliteController.addSatelliteEntity(satId, satName, {
                      line1: processedLine1,
                      line2: processedLine2
                    });
      
      if (!entity) {
        console.error(`无法为卫星 ${satId} 创建实体`);
            return;
          }
          
      try {
        // 先计算轨道周期
        let orbitalPeriod = 90; // 默认值，大约90分钟一个轨道周期
        try {
          // 从TLE第二行解析平均运动（每天圈数）
          const tle2 = processedLine2;
          const meanMotionStr = tle2.substring(52, 63).trim();
          const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
          if (!isNaN(meanMotion) && meanMotion > 0) {
            // 转换为轨道周期（分钟）
            orbitalPeriod = (24 * 60) / meanMotion;
            console.log(`计算卫星 ${satId} 轨道周期: ${orbitalPeriod.toFixed(2)} 分钟`);
          }
        } catch (error) {
          console.warn(`计算卫星轨道周期失败:`, error);
        }
        
        // 获取当前时间
        const now = new Date();
        
        // 扩大计算范围 - 使用TIME_WINDOW_HOURS小时窗口（默认12小时）
        // 确保有足够多的数据点支持轨道显示
        const timeWindowHours = this.TIME_WINDOW_HOURS || 12;
        const calcStartTime = startTime;
        const calcEndTime = new Date(Math.max(
          endTime.getTime(),
          startTime.getTime() + timeWindowHours * 60 * 60 * 1000
        ));
        
        console.log(`扩大计算范围: ${calcStartTime.toISOString()} 到 ${calcEndTime.toISOString()} (${timeWindowHours}小时窗口)`);
        
        // --------------- 优化采样策略开始 ---------------
        // 计算最佳采样间隔 - 改进策略
        // 1. 当前时间附近（前后30分钟）使用高密度采样
        // 2. 轨道周期的1/60（每6度一个点）用于初始轨道显示附近
        // 3. 远处使用较大间隔以平衡性能

        // 基于轨道周期计算采样间隔（保证每个周期至少60个点）
        const baseInterval = Math.min(60, Math.max(5, orbitalPeriod * 60 / 60));
        
        // 计算时间段
        const timeRangeHours = (calcEndTime.getTime() - calcStartTime.getTime()) / (1000 * 60 * 60);
        
        // 对于不同时间段使用不同密度的采样
        // 当前时刻周围的30分钟使用高密度采样
        const currentTimeMinus30 = new Date(now.getTime() - 30 * 60 * 1000);
        const currentTimePlus30 = new Date(now.getTime() + 30 * 60 * 1000);
        
        // 时间轴上的关键点，用于分段采样
        const timePoints = [
          { time: calcStartTime, interval: baseInterval * 2 },  // 开始时间
          { time: currentTimeMinus30, interval: baseInterval }, // 当前时间-30分钟
          { time: currentTimePlus30, interval: baseInterval / 4 }, // 当前时间+30分钟(高密度)
          { time: new Date(now.getTime() + orbitalPeriod * 60 * 1000), interval: baseInterval }, // 一个轨道周期后
          { time: calcEndTime, interval: baseInterval * 2 }   // 结束时间
        ].sort((a, b) => a.time.getTime() - b.time.getTime());
        
        // 去除重叠的时间点
        const uniqueTimePoints = [];
        for (let i = 0; i < timePoints.length; i++) {
          if (i === 0 || timePoints[i].time.getTime() > timePoints[i-1].time.getTime() + 60000) {
            uniqueTimePoints.push(timePoints[i]);
          }
        }
        
        console.log(`使用分段采样策略，共 ${uniqueTimePoints.length} 个时间段:`);
        for (let i = 0; i < uniqueTimePoints.length; i++) {
          console.log(`- 时间段 ${i+1}: ${uniqueTimePoints[i].time.toISOString()}, 采样间隔: ${uniqueTimePoints[i].interval.toFixed(1)}秒`);
        }
        
        // 获取卫星的采样位置属性
        const sampledPosition = this.getSatelliteSampledPosition(satId);
        if (!sampledPosition) {
          console.error(`无法获取卫星 ${satId} 的采样位置属性，跳过添加位置点`);
          return;
        }
        
        // 设置更高精度的插值选项
        sampledPosition.setInterpolationOptions({
          interpolationDegree: 5, // 提高插值阶数，使运动更平滑
          interpolationAlgorithm: Cesium.LagrangePolynomialApproximation // 使用拉格朗日多项式插值
        });
        
        // 分段计算卫星位置
        let allPositions: any[] = [];
        
        // 遍历时间段进行分段计算
        for (let i = 0; i < uniqueTimePoints.length - 1; i++) {
          const segmentStart = uniqueTimePoints[i].time;
          const segmentEnd = uniqueTimePoints[i+1].time;
          const segmentInterval = uniqueTimePoints[i].interval;
          
          console.log(`计算时间段 ${i+1}: ${segmentStart.toISOString()} 到 ${segmentEnd.toISOString()}, 采样间隔: ${segmentInterval.toFixed(1)}秒`);
          
          // 计算当前段的位置
          const positionsResult = OrbitCalculator.calculatePositionsRange(
            satId,
            satName,
            processedLine1,
            processedLine2,
            segmentStart,
            segmentEnd,
            segmentInterval
          );
          
          // 合并结果
          const segmentPositions = positionsResult[satId] || [];
          allPositions = allPositions.concat(segmentPositions);
          
          console.log(`时间段 ${i+1} 计算完成，共 ${segmentPositions.length} 个位置点`);
        }
        // --------------- 优化采样策略结束 ---------------
        
        console.log(`计算了卫星 ${satId} 的 ${allPositions.length} 个位置点，时间跨度: ${(timeRangeHours).toFixed(1)}小时`);
        
      if (allPositions.length < 2) {
          console.warn(`位置点不足，无法创建轨道`);
        return;
      }
      
        // 添加采样点
        let addedCount = 0;
        let errorCount = 0;
        
        for (const posData of allPositions) {
          try {
            if (!posData || !posData.time) {
              errorCount++;
              continue;
            }
            
            const time = Cesium.JulianDate.fromIso8601(posData.time);
            
            if (!posData.position || 
                typeof posData.position.x !== 'number' || 
                typeof posData.position.y !== 'number' || 
                typeof posData.position.z !== 'number' ||
                isNaN(posData.position.x) ||
                isNaN(posData.position.y) ||
                isNaN(posData.position.z)) {
              errorCount++;
              continue;
            }
            
            // 从位置数据创建Cartesian3坐标 - 直接使用经纬度坐标
            const cartesian = Cesium.Cartesian3.fromDegrees(
                posData.longitude,
                posData.latitude,
              posData.altitude
            );
            
            sampledPosition.addSample(time, cartesian);
            addedCount++;
        } catch (error) {
            errorCount++;
          }
        }
        
        console.log(`总共已添加 ${addedCount} 个位置点到卫星 ${satId} 的采样属性中，${errorCount} 个失败`);
        
        // 确保实体可见性范围覆盖整个时间段
        if (entity) {
          entity.availability = new Cesium.TimeIntervalCollection([
            new Cesium.TimeInterval({
              start: Cesium.JulianDate.fromDate(startTime), // 设置为原始时间范围的起始时间
              stop: Cesium.JulianDate.fromDate(calcEndTime) // 使用扩展后的结束时间
            })
          ]);
          console.log(`已设置卫星 ${satId} 的可见性时间范围: ${startTime.toISOString()} 到 ${calcEndTime.toISOString()}`);
          
          // 设置轨道显示只显示一个周期
          const periodInSeconds = orbitalPeriod * 60; // 转换为秒
          
          // 将轨道线显示为卫星未来一个完整轨道周期的轨迹
          // leadTime显示"未来"轨迹，trailTime显示"历史"轨迹
          entity.path.leadTime = periodInSeconds; // 显示未来一个完整轨道周期
          entity.path.trailTime = 1; // 设为1秒，几乎不显示历史轨迹
          
          // 修改轨道线外观为发光线
          const color = new Cesium.Color(0.7, 0.7, 1.0, 1.0); // 淡蓝色基础颜色
          
          // 设置为发光线，细一点，不使用虚线
          entity.path.material = new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.2, // 发光强度
            color: color.withAlpha(0.8), // 轨道线颜色
            taperPower: 0.5 // 发光边缘渐变
          });
          
          // 设置轨道线宽度
          entity.path.width = 1.5; // 更细的线条
          
          // 确保轨道显示尊重全局可见性设置
          entity.path.show = this.orbitsVisible;
          
          console.log(`设置卫星 ${satId} 轨道显示参数: leadTime=${periodInSeconds}秒, trailTime=1秒 (轨道周期: ${orbitalPeriod.toFixed(2)}分钟)`);
          console.log(`卫星 ${satId} 轨道显示设置完成，显示未来一个完整轨道周期，使用发光线样式，轨道可见性: ${this.orbitsVisible ? '可见' : '隐藏'}`);
          
          // 初始化轨道更新跟踪器 - 将时间设为现在，确保下次按正常时间间隔更新
          this.lastOrbitUpdateTimes.set(satId, new Date());
          
          // 确保卫星实体显示
          entity.show = true;
          console.log(`卫星 ${satId} 已设置为可见，计算${timeRangeHours.toFixed(1)}小时的位置数据完成`);
          } else {
            console.warn(`未找到卫星 ${satId} 的实体`);
          }
        
        // 强制更新场景
        this.viewer.scene.requestRender();
        
        } catch (error) {
        console.error(`计算卫星位置时出错:`, error);
        // 出错时移除实体
        this.satelliteController.removeSatellite(satId);
            }
          } catch (error) {
      console.error(`计算卫星 ${satId} 位置时出错:`, error);
    }
  }

  /**
   * 显示卫星轨道和位置
   * @param data API返回的卫星位置数据
   */
  displaySatellites(data: any): void {
    console.log('displaySatellites被调用，数据:', data);
    
    try {
      // 在显示新卫星前，先清除现有的卫星和轨道实体
      if (this.satelliteController) {
        // 同时清除缓存数据，避免尝试访问已删除的卫星
        this.satelliteDataCache.clear();
        this.lastOrbitUpdateTimes.clear();
        
        // 清除卫星控制器中的所有实体
        this.satelliteController.clearAll();
        console.log('已清除所有现有卫星和轨道实体以及相关缓存数据');
      }
      
      // 检查是否有positions属性，并且是否指示已有TLE数据
      if (data.positions && data.hasTleData === true) {
        // 转换为Satellite格式的数组
        const satellites: any[] = [];
        
        // 遍历positions对象中的每个卫星数据
        for (const satId in data.positions) {
          if (Object.prototype.hasOwnProperty.call(data.positions, satId)) {
            const satData = data.positions[satId];
            
            // 检查必要的数据是否存在
            if (!satData.line1 || !satData.line2) {
              console.warn(`卫星 ${satId} 缺少必要的TLE数据，跳过处理`);
              continue;
            }
            
            // 创建卫星对象
            const satellite = {
              id: satId,
              name: satData.name || `Satellite ${satId}`,
              satId: satId,
              line1: satData.line1,
              line2: satData.line2,
              // 如果有constellation属性，直接使用
              constellation: satData.constellation,
              // 将position和velocity添加到卫星对象中，以备将来需要
              position: satData.position,
              velocity: satData.velocity,
              // 适配orbitInfo格式
              orbitInfo: {
                tle: [satData.line1, satData.line2]
              }
            };
            
            satellites.push(satellite);
          }
        }
        
        // 调用displaySatellitesInTimeRange来显示卫星
        if (satellites.length > 0) {
          console.log(`准备显示 ${satellites.length} 颗卫星:`, satellites);
          this.displaySatellitesInTimeRange(satellites);
        } else {
          console.warn('没有有效的卫星数据可以显示');
          message.warning('没有有效的卫星数据可以显示');
        }
      } else {
        // 旧的数据处理方式
        console.log('不支持的数据格式或缺少TLE数据:', data);
        message.warning('不支持的数据格式或缺少必要的TLE数据');
      }
    } catch (error) {
      console.error('显示卫星时出错:', error);
      message.error('显示卫星时出错: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 在指定时间范围内显示卫星
   * @param satellites 卫星数据
   */
  public displaySatellitesInTimeRange(satellites: Satellite[]): void {
    try {
      console.log(`准备在时间范围内显示 ${satellites.length} 颗卫星`);
      
      if (!satellites || satellites.length === 0) {
        console.warn('没有卫星数据可显示');
        console.info('传入的satellites数组为空或未定义');
        // 显示提示信息
        message.warning('没有卫星数据可显示');
        return;
      }
      
      // 获取当前时间和时间轴范围 - 扩展到6天范围（前3天，后3天）
      const now = new Date();
      const currentJulianDate = Cesium.JulianDate.fromDate(now);
      
      // 修改：使用6天的时间范围（前3天，后3天）
      const startDate = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000); // 3天前
      const endDate = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000); // 3天后
      
      // 转换为Cesium Julian日期
      const julianStartTime = Cesium.JulianDate.fromDate(startDate);
      const julianEndTime = Cesium.JulianDate.fromDate(endDate);
      
      // 设置时钟范围
      this.viewer.clock.startTime = julianStartTime;
      this.viewer.clock.stopTime = julianEndTime;
      this.viewer.clock.currentTime = currentJulianDate;
      this.viewer.clock.multiplier = 10; // 加快时间流逝
      this.viewer.clock.shouldAnimate = true;
      
      // 设置时间线显示范围
      this.viewer.timeline.zoomTo(julianStartTime, julianEndTime);
      
      console.log(`设置时间轴范围: ${startDate.toISOString()} 到 ${endDate.toISOString()}`);
      
      // 清空现有的卫星数据缓存
      this.satelliteDataCache.clear();
      
      // 分批处理配置
      const batchSize = 100; // 每批处理的卫星数量
      const batchDelay = 10; // 批次间延迟(毫秒)
      
      // 显示加载进度通知 - 立即显示进度条
      const key = `loading-satellites-${Date.now()}`;
      message.loading({ 
        content: `已加载 0 颗卫星 (0%)`, 
        key, 
        duration: 0 
      });
      
      // 添加的卫星计数
      let addedSatelliteCount = 0;
      let processedCount = 0;
      let validSatellitesCount = 0;
      
      // 分批处理卫星数据
      const processSatelliteBatch = async (batchStartIndex: number) => {
        const batchEndIndex = Math.min(batchStartIndex + batchSize, satellites.length);
        console.log(`处理卫星批次: ${batchStartIndex} 到 ${batchEndIndex-1} (共 ${satellites.length} 颗)`);
        
        // 这一批的卫星处理结果
        const batchResults: any[] = [];
        
        // 处理这一批的每颗卫星
        for (let i = batchStartIndex; i < batchEndIndex; i++) {
          const satellite = satellites[i];
          
          // 检查TLE数据 - 支持多种可能的TLE数据格式
          let tleLine1 = '';
          let tleLine2 = '';
          
          // 尝试从不同的可能位置获取TLE数据
          if (satellite.line1 && satellite.line2) {
            // 格式1: 直接在根对象上有line1和line2
            tleLine1 = satellite.line1;
            tleLine2 = satellite.line2;
          } else if (satellite.orbitInfo?.tle && Array.isArray(satellite.orbitInfo.tle)) {
            // 格式2: 在orbitInfo.tle数组中
            tleLine1 = satellite.orbitInfo.tle[0] || '';
            tleLine2 = satellite.orbitInfo.tle[1] || '';
          } else if (satellite.tle) {
            // 格式3: 在tle对象中
            if (typeof satellite.tle === 'object') {
              if (satellite.tle.line1 && satellite.tle.line2) {
                // 格式3.1: {line1: '...', line2: '...'}
                tleLine1 = satellite.tle.line1;
                tleLine2 = satellite.tle.line2;
              } else if (Array.isArray(satellite.tle) && satellite.tle.length >= 2) {
                // 格式3.2: tle是数组
                tleLine1 = satellite.tle[0];
                tleLine2 = satellite.tle[1];
              }
            }
          } else if (typeof satellite === 'object' && satellite['positions']) {
            // 格式4: 在positions对象中的特定卫星ID下
            const satId = satellite.id || Object.keys(satellite['positions'])[0];
            if (satId && satellite['positions'][satId]) {
              const posData = satellite['positions'][satId];
              if (posData.line1 && posData.line2) {
                tleLine1 = posData.line1;
                tleLine2 = posData.line2;
              }
            }
          }
          
          // 验证TLE格式
          if (!tleLine1 || !tleLine2 || tleLine1.length < 10 || tleLine2.length < 10) {
            console.warn(`卫星 ${satellite.name || '未知'} (ID: ${satellite.id || '未知'}) 缺少有效的TLE数据，无法显示`);
            processedCount++;
            continue;
          } 
          
          // 获取卫星名称和ID
          const satId = satellite.id || satellite.satId || '';
          const satName = satellite.name || `Satellite ${satId}`;
          
          try {
            console.log(`开始处理卫星 ${satName} (ID: ${satId})`);
            
            // 首先确定卫星所属星座
            const constellationName = this.determineConstellation(satName);
            
            // 计算卫星位置
            await this.calculateAndAddSatellitePositions(
              satId,
              satName,
              tleLine1,
              tleLine2,
              startDate,
              endDate,
              60
            );
            
            // 成功处理卫星后，添加到结果
            batchResults.push({
              satId: satId,
              satName: satName,
              constellationName: constellationName,
              tleLine1: tleLine1,
              tleLine2: tleLine2,
              sampledPosition: this.getSatelliteSampledPosition(satId)
            });
            validSatellitesCount++;
          } catch (error) {
            console.error(`处理卫星 ${satName} (${satId}) 时出错:`, error);
          }
          
          processedCount++;
          
          // 实时更新进度条，但控制频率避免过多UI更新
          if (processedCount % 10 === 0 || processedCount === satellites.length) {
            const progress = Math.round((processedCount / satellites.length) * 100);
            message.loading({ 
              content: `已加载 ${addedSatelliteCount} 颗卫星 (${progress}%)`, 
              key, 
              duration: 0 
            });
          }
        }
        
        // 批次处理完成，显示这批卫星
        console.log(`批次处理完成，显示 ${batchResults.length} 颗卫星`);
        
        // 添加这批卫星到场景
        for (const sat of batchResults) {
          if (!sat || !sat.sampledPosition) {
            console.warn(`卫星 ${sat?.satName || 'unknown'} 缺少采样位置数据，跳过显示`);
            continue;
          }
          
          // 确保每颗卫星都有真正唯一的ID
          const uniqueSatId = `SATELLITE-${sat.satId}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
          
          try {
            // 添加卫星到场景
            this.satelliteController.addSatellite({
              id: uniqueSatId, 
              name: sat.satName,
              satnum: sat.satId,
              constellationName: sat.constellationName,
              sampledPosition: sat.sampledPosition,
              tle: {
                line1: sat.tleLine1,
                line2: sat.tleLine2
              },
              orbitsVisible: this.orbitsVisible // 确保传递当前的轨道可见性状态
            });
            
            // 追踪卫星以便未来更新
            this.satelliteController.trackSatellite(uniqueSatId, {
              line1: sat.tleLine1,
              line2: sat.tleLine2
            }, {
              name: sat.satName
            });
            
            // 初始化缓存信息，添加到satelliteDataCache
            this.satelliteDataCache.set(uniqueSatId, {
              lastCalculationTime: startDate,
              nextCalculationTime: endDate,
              calculating: false
            });
            
            addedSatelliteCount++;
          } catch (error) {
            console.error(`添加卫星 ${sat.satName} (${uniqueSatId}) 时出错:`, error);
          }
        }
        
        // 更新加载进度通知
        const progress = Math.round((processedCount / satellites.length) * 100);
        message.loading({ 
          content: `已加载 ${addedSatelliteCount} 颗卫星 (${progress}%)`, 
          key, 
          duration: 0 
        });
        
        // 强制刷新场景
        this.viewer.scene.requestRender();
        
        // 检查是否有更多批次需要处理
        if (batchEndIndex < satellites.length) {
          // 使用setTimeout进行下一批处理，给UI一些刷新时间
            setTimeout(() => {
            processSatelliteBatch(batchEndIndex);
          }, batchDelay);
        } else {
          // 所有批次处理完成
          console.log(`所有卫星处理完成，共显示 ${addedSatelliteCount}/${validSatellitesCount} 颗卫星`);
          
          // 启用自动位置更新机制
          if (addedSatelliteCount > 0) {
            // 确保设置或重置时钟监听器，每帧更新卫星轨道显示
            this.setupClockListener();
            
            // 确保轨道线更新至少执行一次
            const currentTime = Cesium.JulianDate.toDate(this.viewer.clock.currentTime);
            this.checkAndUpdateSatellitePositions(currentTime);
            
            // 特别记录：为避免卫星在轨道边缘消失，强制立即更新所有轨道线
            for (const [satId, _] of this.satelliteDataCache.entries()) {
              // 清除上次更新时间，强制下次检查时更新
              this.lastOrbitUpdateTimes.set(satId, new Date(0));
            }
          }
          
          // 处理完成后，关闭进度条
          message.destroy(key);
        }
      };
      
      // 开始处理第一批
      processSatelliteBatch(0);
      
    } catch (error) {
      console.error('显示卫星时发生错误:', error);
      message.error('显示卫星时发生错误');
    }
  }

  /**
   * 获取卫星采样位置属性
   * @param satId 卫星ID
   * @returns 采样位置属性或null
   */
  private getSatelliteSampledPosition(satId: string): Cesium.SampledPositionProperty | null {
    try {
      if (!this.satelliteController) {
        console.warn(`卫星控制器未初始化，无法获取卫星 ${satId} 的采样位置`);
                  return null;
      }
      
      // 从satelliteController获取实体
      const entity = this.satelliteController.satellites.get(satId);
      
      if (!entity) {
        console.warn(`未找到卫星 ${satId} 的实体`);
                  return null;
                }
      
      // 确保实体有position属性
      if (!entity.position) {
        console.warn(`卫星 ${satId} 的实体没有position属性`);
        return null;
      }
      
      // 获取采样位置属性
      const sampledPosition = entity.position as Cesium.SampledPositionProperty;
      
      // 重要：确保参考系为FIXED，这是解决卫星从东向西运动问题的关键
      if ((sampledPosition as any).referenceFrame !== Cesium.ReferenceFrame.FIXED) {
        console.log(`修正卫星 ${satId} 的采样位置属性参考系为FIXED（之前是${(sampledPosition as any).referenceFrame}）`);
        (sampledPosition as any).referenceFrame = Cesium.ReferenceFrame.FIXED;
      }
      
      return sampledPosition;
            } catch (error) {
      console.error(`获取卫星 ${satId} 采样位置时出错:`, error);
              return null;
    }
  }

  /**
   * 根据卫星名称确定所属星座
   * @param satelliteName 卫星名称
   * @returns 星座名称
   */
  private determineConstellation(satelliteName: string): string {
    const name = satelliteName.toLowerCase();
    
    if (name.includes('starlink')) return 'starlink';
    if (name.includes('oneweb')) return 'oneweb';
    if (name.includes('beidou') || name.includes('bei dou')) return 'beidou';
    if (name.includes('galileo')) return 'galileo';
    if (name.includes('gps')) return 'gps';
    if (name.includes('glonass')) return 'glonass';
    if (name.includes('iridium')) return 'iridium';
    if (name.includes('globalstar')) return 'globalstar';
    if (name.includes('cosmos')) return 'cosmos';
    if (name.includes('kuiper')) return 'kuiper';
    
    return 'default';
  }

  /**
   * 分批计算剩余时间段的卫星位置
   * @param satId 卫星ID
   * @param satName 卫星名称
   * @param tleLine1 TLE第一行
   * @param tleLine2 TLE第二行
   * @param segments 时间段数组
   */
  private scheduleRemainingSegmentsCalculation(
    satId: string, 
    satName: string, 
    tleLine1: string, 
    tleLine2: string, 
    segments: Array<{start: Date, end: Date}>
  ): void {
    // 先计算轨道周期，用于确定采样间隔
    let orbitalPeriod = 90; // 默认90分钟
    try {
      // 从TLE第二行解析平均运动（每天圈数）
      const tle2 = tleLine2.trim();
      const meanMotionStr = tle2.substring(52, 63).trim();
      const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
      if (!isNaN(meanMotion) && meanMotion > 0) {
        // 转换为轨道周期（分钟）
        orbitalPeriod = (24 * 60) / meanMotion;
        console.log(`调度计算 - 卫星 ${satId} 轨道周期: ${orbitalPeriod.toFixed(2)} 分钟`);
      }
    } catch (error) {
      console.warn(`调度计算 - 计算卫星轨道周期失败:`, error);
    }
    
    // 基于轨道周期计算基础采样间隔（每个轨道周期至少60个点）
    const baseInterval = Math.min(60, Math.max(5, orbitalPeriod * 60 / 60));
    console.log(`调度计算 - 卫星 ${satId} 基础采样间隔: ${baseInterval.toFixed(1)}秒`);
    
    // 获取当前时间
    const now = new Date();
    
    // 跳过第一个段（已计算）
    for (let i = 1; i < segments.length; i++) {
      // 使用setTimeout和闭包确保每个计算任务独立且不会阻塞UI
      const delay = (i - 1) * 300; // 每个段之间间隔300毫秒，避免浏览器卡顿
      
      setTimeout(async () => {
        const segment = segments[i];
        
        try {
          console.log(`开始计算卫星 ${satId} 时间段#${i+1}: ${segment.start.toISOString()} 到 ${segment.end.toISOString()}`);
          
          // 智能调整采样间隔
          let sampleInterval = baseInterval * 2; // 默认使用较大间隔
          
          // 如果当前时间在此段内，使用更密集的采样
          if (now >= segment.start && now <= segment.end) {
            sampleInterval = baseInterval / 4; // 使用更高精度的采样
            console.log(`当前时间在时间段#${i+1}内，使用高精度采样间隔: ${sampleInterval.toFixed(1)}秒`);
          }
          // 如果此段与当前时间相近，使用较密集的采样
          else if (Math.abs(segment.start.getTime() - now.getTime()) < 30 * 60 * 1000 ||
                   Math.abs(segment.end.getTime() - now.getTime()) < 30 * 60 * 1000) {
            sampleInterval = baseInterval / 2; // 较高精度
            console.log(`时间段#${i+1}与当前时间相近，使用较高精度采样间隔: ${sampleInterval.toFixed(1)}秒`);
          }
          
          await this.calculateAndAddSatellitePositions(
            satId,
            satName,
            tleLine1,
            tleLine2,
            segment.start,
            segment.end,
            sampleInterval // 使用智能调整的采样间隔
          );
          
          console.log(`完成卫星 ${satId} 时间段#${i+1}的计算`);
          
          // 更新satelliteDataCache
          this.updateSatelliteDataCache(satId, segment.start, segment.end);
          
        } catch (error) {
          console.error(`计算卫星 ${satId} 时间段#${i+1}时出错:`, error);
        }
      }, delay);
    }
  }
  
  /**
   * 更新卫星数据缓存信息
   */
  private updateSatelliteDataCache(satId: string, startTime: Date, endTime: Date): void {
    const cacheInfo = this.satelliteDataCache.get(satId);
    if (cacheInfo) {
      // 更新缓存的时间范围，取最小的开始时间和最大的结束时间
      if (startTime < cacheInfo.lastCalculationTime) {
        cacheInfo.lastCalculationTime = startTime;
      }
      
      if (endTime > cacheInfo.nextCalculationTime) {
        cacheInfo.nextCalculationTime = endTime;
      }
      
      console.log(`更新卫星 ${satId} 的计算缓存: ${cacheInfo.lastCalculationTime.toISOString()} 至 ${cacheInfo.nextCalculationTime.toISOString()}`);
    }
  }

  /**
   * 确保TLE行格式正确
   * @param line TLE行
   * @param lineNumber 行号（1或2）
   * @returns 处理后的TLE行
   */
  private ensureTLEFormat(line: string, lineNumber: number): string {
    if (!line || typeof line !== 'string' || line.trim().length < 10) {
      console.error(`TLE第${lineNumber}行格式错误: ${line}`);
      return '';
    }
    
    let processedLine = line.trim();
    
    // 确保行以正确的行号开头
    if (lineNumber === 1 && !processedLine.startsWith('1 ')) {
      processedLine = `1 ${processedLine.startsWith('1') ? processedLine.substring(1).trim() : processedLine}`;
    } else if (lineNumber === 2 && !processedLine.startsWith('2 ')) {
      processedLine = `2 ${processedLine.startsWith('2') ? processedLine.substring(1).trim() : processedLine}`;
    }
    
    return processedLine;
  }

  /**
   * 设置Cesium时钟到指定时间
   * @param date 要设置的日期时间
   */
  public setCurrentTime(date: Date): void {
    // 转换为JulianDate
    const julianDate = Cesium.JulianDate.fromDate(date);
    
    // 设置当前时间
    this.viewer.clock.currentTime = julianDate;
    
    console.log(`已将Cesium时钟设置为: ${date.toISOString()}`);
  }

  /**
   * 将所有活跃卫星同步到当前系统时间
   * 处理实时按钮点击事件
   */
  public syncAllSatellitesToCurrentTime(): void {
    // 获取当前系统时间
    const currentTime = new Date();
    
    // 设置Cesium时钟到当前时间
    this.setCurrentTime(currentTime);
    
    // 设置时钟速率为1.0（实时速度）
    this.setTimeMultiplier(1.0);
    
    // 设置正确的时钟模式（与初始化相同）
    this.viewer.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK_MULTIPLIER;
    
    // 确保时钟在运行
    this.startClock();
    
    // 获取所有活跃卫星
    const activeSatellites = this.satelliteController.getAllTrackedSatellites();
    
    // 循环更新每个卫星
    for (const satId of activeSatellites) {
      // 获取卫星TLE数据
      const satellite = this.satelliteController.getTrackedSatellite(satId);
      if (!satellite || !satellite.line1 || !satellite.line2) continue;
      
      // 更新卫星位置到当前时间
      this.updateSatellitePositionToTime(satId, currentTime);
      
      // 重新计算轨道线
      this.updateSatelliteOrbit(satId, currentTime);
      
      // 重置轨道更新时间，以便在下一次检查时进行更新
      this.lastOrbitUpdateTimes.set(satId, new Date(0));
    }
    
    // 强制场景更新
    this.viewer.scene.requestRender();
    
    console.log(`已同步 ${activeSatellites.length} 颗卫星到当前时间: ${currentTime.toISOString()}, 时钟模式: SYSTEM_CLOCK_MULTIPLIER, 倍率: 1.0`);
  }

  /**
   * 更新卫星位置到指定时间
   * @param satId 卫星ID
   * @param time 目标时间
   */
  private updateSatellitePositionToTime(satId: string, time: Date): void {
    // 检查卫星跟踪数据是否存在
    const satellite = this.satelliteController.getTrackedSatellite(satId);
    if (!satellite || !satellite.line1 || !satellite.line2) {
      // 卫星已被删除或缺少TLE数据，清理相关缓存
      this.satelliteDataCache.delete(satId);
      this.lastOrbitUpdateTimes.delete(satId);
      return;
    }
    
    // 计算指定时间的卫星位置
    try {
      const julianDate = Cesium.JulianDate.fromDate(time);
      // 在传递给OrbitCalculator之前确保TLE数据格式正确
      const line1 = satellite.line1.trim();
      const line2 = satellite.line2.trim();
      
      // 确保line1以"1 "开头，line2以"2 "开头
      const formattedLine1 = line1.startsWith('1 ') ? line1 : `1 ${line1}`;
      const formattedLine2 = line2.startsWith('2 ') ? line2 : `2 ${line2}`;
      
      const position = OrbitCalculator.calculatePosition(
        { 
          id: satellite.id,
          line1: formattedLine1, 
          line2: formattedLine2 
        },
        time
      );
      
      // 获取卫星实体
      const entity = this.viewer.entities.getById(satId);
      if (!entity) {
        // 卫星实体不存在，可能已被删除，清理相关缓存
        this.satelliteDataCache.delete(satId);
        this.lastOrbitUpdateTimes.delete(satId);
        return;
      }
      
      if (entity && position) {
        // 如果实体使用SampledPositionProperty，更新其初始点
        if (entity.position instanceof Cesium.SampledPositionProperty) {
          entity.position.addSample(julianDate, position);
          
          // 确保实体可见
          if (entity.billboard) {
            entity.billboard.show = true;
          }
        }
      }
    } catch (error) {
      console.error(`更新卫星 ${satId} 位置时出错:`, error);
    }
  }

  /**
   * 更新卫星轨道线
   * @param satId 卫星ID
   * @param currentTime 当前时间
   */
  private updateSatelliteOrbit(satId: string, currentTime: Date): void {
    // 检查卫星跟踪数据是否存在
    const satellite = this.satelliteController.getTrackedSatellite(satId);
    if (!satellite || !satellite.line1 || !satellite.line2) {
      // 卫星已被删除或缺少TLE数据，清理相关缓存
      this.satelliteDataCache.delete(satId);
      this.lastOrbitUpdateTimes.delete(satId);
      return;
    }
    
    // 获取卫星实体
    const entity = this.viewer.entities.getById(satId);
    if (!entity || !entity.path) {
      // 卫星实体不存在或没有轨道路径，清理相关缓存
      this.satelliteDataCache.delete(satId);
      this.lastOrbitUpdateTimes.delete(satId);
      return;
    }
    
    try {
      // 获取轨道周期（分钟）
      let orbitalPeriod = 90; // 默认90分钟
      
      // 确保TLE数据格式正确
      const line1 = satellite.line1.trim();
      const line2 = satellite.line2.trim();
      
      // 确保line1以"1 "开头，line2以"2 "开头
      const formattedLine1 = line1.startsWith('1 ') ? line1 : `1 ${line1}`;
      const formattedLine2 = line2.startsWith('2 ') ? line2 : `2 ${line2}`;
      
      try {
        // 从TLE第二行解析平均运动
        const meanMotionStr = formattedLine2.substring(52, 63).trim();
        const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
        if (!isNaN(meanMotion) && meanMotion > 0) {
          orbitalPeriod = (24 * 60) / meanMotion;
        }
      } catch (error) {
        console.warn(`计算卫星轨道周期失败:`, error);
      }
      
      // 计算轨道周期（秒）
      const periodInSeconds = orbitalPeriod * 60;
      
      // 更新轨道线显示参数
      entity.path.leadTime = periodInSeconds; // 显示未来一个完整轨道周期
      entity.path.trailTime = 1; // 设为1秒，几乎不显示历史轨迹
      
      // 确保轨道线可见
      entity.path.show = true;
      
      console.log(`实时按钮：已更新卫星 ${satId} 轨道线，显示未来一个完整轨道周期 (${orbitalPeriod.toFixed(2)}分钟)`);
      
      // 检查是否需要重新计算位置数据
      const cacheInfo = this.satelliteDataCache.get(satId);
      if (cacheInfo) {
        const timeToDataEndMs = cacheInfo.nextCalculationTime.getTime() - currentTime.getTime();
        const minutesToDataEnd = timeToDataEndMs / 60000;
        
        // 如果剩余数据不足一个轨道周期，则重新计算
        if (minutesToDataEnd < orbitalPeriod && !cacheInfo.calculating) {
          console.log(`卫星 ${satId} 接近数据边界，距离数据结束还有 ${minutesToDataEnd.toFixed(1)} 分钟，重新计算位置数据`);
          
          // 标记为正在计算
          cacheInfo.calculating = true;
          
          // 计算新的时间窗口
          const windowSizeMs = (this.TIME_WINDOW_HOURS || 12) * 60 * 60 * 1000;
          const startTime = new Date(currentTime);
          const endTime = new Date(currentTime.getTime() + windowSizeMs);
          
          // 异步计算新的位置数据
          this.calculateAndAddSatellitePositions(
            satId,
            satellite.name || satId,
            formattedLine1,
            formattedLine2,
            startTime,
            endTime,
            30 // 使用较大的采样间隔
          ).then(() => {
            // 更新缓存信息
            cacheInfo.lastCalculationTime = startTime;
            cacheInfo.nextCalculationTime = endTime;
            cacheInfo.calculating = false;
            
            console.log(`卫星 ${satId} 位置数据已更新至 ${endTime.toISOString()}`);
          }).catch(error => {
            console.error(`计算卫星 ${satId} 位置时出错:`, error);
            cacheInfo.calculating = false;
          });
        }
      }
    } catch (error) {
      console.error(`更新卫星 ${satId} 轨道时出错:`, error);
    }
  }

  /**
   * 设置所有轨道的显示状态
   * @param visible 是否显示轨道
   */
  public setAllOrbitsVisibility(visible: boolean): void {
    // 保存全局可见性状态
    this.orbitsVisible = visible;
    
    if (this.satelliteController) {
      this.satelliteController.setAllOrbitsVisibility(visible);
    }
  }

  /**
   * 设置所有卫星名称标签显示/隐藏
   * @param visible 是否可见
   */
  public setAllSatelliteLabelsVisibility(visible: boolean): void {
    // 保存全局可见性状态
    this.labelsVisible = visible;
    
    if (this.satelliteController) {
      this.satelliteController.setAllSatelliteLabelsVisibility(visible);
    }
  }

  /**
   * 获取当前轨道显示状态
   * @returns 如果所有轨道都可见返回true，否则返回false
   */
  public getOrbitsVisibility(): boolean {
    if (this.satelliteController) {
      return this.satelliteController.getOrbitsVisibility();
    }
    return false;
  }

  /**
   * 获取卫星标签的可见性状态
   */
  public getSatelliteLabelsVisibility(): boolean {
    return this.satelliteController.getSatelliteLabelsVisibility();
  }

  /**
   * 初始化LOD控制系统
   */
  private initializeLodControl(): void {
    try {
      // 监听相机移动事件，优化更新频率
      this.viewer.camera.moveEnd.addEventListener(() => {
        // 获取当前相机位置
        const currentPosition = this.camera.position.clone();
        
        // 如果是第一次获取相机位置
        if (!this.lastCameraPosition) {
          this.lastCameraPosition = currentPosition;
          this.updateSatellitesLod();
            return;
          }
          
        // 判断相机位置变化是否超过阈值，减少不必要的更新
        const distance = Cesium.Cartesian3.distance(currentPosition, this.lastCameraPosition);
        if (distance > this.cameraMovementThreshold) {
          // 保存新的相机位置
          this.lastCameraPosition = currentPosition;
          
          // 使用延迟更新，防止快速相机移动时频繁触发
          this.handleCameraMovement();
        }
      });
      
      console.log('LOD控制系统已初始化');
    } catch (error) {
      console.error('初始化LOD控制系统时出错:', error);
    }
  }
  
  /**
   * 相机移动处理器
   */
  private handleCameraMovement(): void {
    try {
      // 防抖处理，确保不会频繁更新
      if (this.lodUpdateTimeout) {
        clearTimeout(this.lodUpdateTimeout);
      }
      
      // 延迟更新，等待相机稳定
      this.lodUpdateTimeout = setTimeout(() => {
        this.updateSatellitesLod();
      }, 500); // 500毫秒延迟
    } catch (error) {
      console.error('处理相机移动时出错:', error);
    }
  }
  
  /**
   * 更新所有卫星的LOD状态
   */
  private updateSatellitesLod(): void {
    try {
      // 获取当前相机高度
      const cameraPosition = this.camera.position;
      const ellipsoidPosition = this.scene.globe.ellipsoid.cartesianToCartographic(cameraPosition);
      const cameraHeight = ellipsoidPosition.height;
      
      // 分批更新卫星LOD，避免一次性处理太多实体导致卡顿
      this.updateSatelliteLodBatch(cameraHeight);
    } catch (error) {
      console.error('更新卫星LOD时出错:', error);
    }
  }
  
  /**
   * 分批更新卫星LOD
   * @param cameraHeight 相机高度
   */
  private updateSatelliteLodBatch(cameraHeight: number): void {
    try {
      // 获取所有卫星实体ID
      const allSatelliteIds = this.satelliteController.getAllSatellites();
      if (!allSatelliteIds || allSatelliteIds.length === 0) return;
      
      console.log(`更新 ${allSatelliteIds.length} 颗卫星的LOD显示状态，当前相机高度: ${(cameraHeight / 1000).toFixed(0)}km`);
      
      // 根据相机高度确定详细程度
      let detailLevel: 'high' | 'medium' | 'low' = 'high';
      if (cameraHeight > this.LOD_FAR_DISTANCE) {
        detailLevel = 'low';
      } else if (cameraHeight > this.LOD_MID_DISTANCE) {
        detailLevel = 'medium';
      }
      
      // 创建视锥体
      let cullingVolume: Cesium.CullingVolume | undefined;
      try {
        const frustum = this.camera.frustum;
        const cameraPosition = this.camera.position;
        const cameraDirection = this.camera.direction;
        const cameraUp = this.camera.up;
        
        // 创建视锥体并检查其有效性
        cullingVolume = frustum.computeCullingVolume(cameraPosition, cameraDirection, cameraUp);
        console.log(`[LOD Update] Computed CullingVolume:`, cullingVolume);
        console.log(`[LOD Update] Type of CullingVolume: object`);
        
        // 检查cullingVolume是否具有computeVisibility方法
        const methodExists = cullingVolume && 'computeVisibility' in cullingVolume;
        console.log(`[LOD Update] computed containsPoint method exists: ${methodExists}`);
        
        if (!methodExists) {
          // 如果没有正确的方法，我们不使用视锥体剔除
          console.warn('[LOD Update] CullingVolume对象没有computeVisibility方法，禁用视锥体剔除');
          cullingVolume = undefined;
        }
      } catch (cullingError) {
        console.error('[LOD Update] 创建视锥体时出错:', cullingError);
        cullingVolume = undefined; // 出错时不使用视锥体剔除
      }
      
      // 如果显示的卫星数量低于最大限制，不需要进一步筛选和隐藏
      if (allSatelliteIds.length <= this.MAX_VISIBLE_SATELLITES) {
        this.applyLodToAllSatellites(allSatelliteIds, detailLevel, cullingVolume);
        return;
      }
      
      // 当卫星数量超过最大可见数量时，根据距离和重要性进行筛选
      try {
        // 计算每个卫星到相机的距离和是否在视锥体内
        const satelliteDistances = allSatelliteIds.map(satId => {
          try {
            const entity = this.viewer.entities.getById(satId);
            if (!entity || !entity.position) return { id: satId, distance: Number.MAX_VALUE, inView: false };
            
            // 获取卫星当前位置
            const satPosition = entity.position.getValue(this.viewer.clock.currentTime);
            if (!satPosition) return { id: satId, distance: Number.MAX_VALUE, inView: false };
            
            // 计算与相机的距离
            const distance = Cesium.Cartesian3.distance(this.camera.position, satPosition);
            
            // 检查是否在视锥体内
            let inView = true; // 默认在视野内
            if (cullingVolume && 'computeVisibility' in cullingVolume) {
              try {
                // 创建包围球并检查可见性
                const boundingSphere = new Cesium.BoundingSphere(satPosition, 1.0);
                inView = cullingVolume.computeVisibility(boundingSphere) !== Cesium.Intersect.OUTSIDE;
              } catch (visibilityError) {
                console.error(`检查卫星 ${satId} 可见性时出错:`, visibilityError);
                // 出错时假设卫星可见
                inView = true;
              }
            }
            
            return { id: satId, distance, inView };
          } catch (entityError) {
            console.error(`处理卫星 ${satId} 距离计算时出错:`, entityError);
            return { id: satId, distance: Number.MAX_VALUE, inView: false };
          }
        });
        
        // 先选择视锥体内的卫星，然后按距离排序
        const inViewSatellites = satelliteDistances.filter(sat => sat.inView).sort((a, b) => a.distance - b.distance);
        const outOfViewSatellites = satelliteDistances.filter(sat => !sat.inView).sort((a, b) => a.distance - b.distance);
        
        // 合并排序结果，视锥体内的卫星优先
        const sortedSatellites = [...inViewSatellites, ...outOfViewSatellites];
        
        // 根据卫星数量计算显示策略
        
        // 1. 选择最近的一部分卫星完整显示
        const visibleSatelliteIds = sortedSatellites
          .slice(0, this.MAX_VISIBLE_SATELLITES)
          .map(item => item.id);
        
        // 2. 计算可以显示标签的卫星
        const satellitesWithLabels = sortedSatellites
          .slice(0, this.MAX_VISIBLE_SATELLITES_WITH_LABELS)
          .map(item => item.id);
        
        // 3. 计算可以显示轨道的卫星
        const satellitesWithOrbits = sortedSatellites
          .slice(0, this.MAX_VISIBLE_ORBITS)
          .map(item => item.id);
        
        // 4. 对于其余卫星，隐藏显示
        const hiddenSatelliteIds = sortedSatellites
          .slice(this.MAX_VISIBLE_SATELLITES)
          .map(item => item.id);
        
        // 应用LOD设置
        console.log(`LOD分配: 可见=${visibleSatelliteIds.length}, 有标签=${satellitesWithLabels.length}, 有轨道=${satellitesWithOrbits.length}, 隐藏=${hiddenSatelliteIds.length}`);
        
        // 对可见卫星应用LOD设置
        visibleSatelliteIds.forEach(satId => {
          const entity = this.viewer.entities.getById(satId);
          if (!entity) return;
          
          // 设置实体可见
          entity.show = true;
          
          // 设置图标可见
          if (entity.billboard) {
            entity.billboard.show = true;
            
            // 根据相机距离调整大小
            if (detailLevel === 'low') {
              entity.billboard.scale = 0.2;
              // 对远距离卫星禁用pickable以减少拾取计算
              entity.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;
            } else if (detailLevel === 'medium') {
              entity.billboard.scale = 0.3;
              entity.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;
              } else {
              entity.billboard.scale = 0.4;
              entity.billboard.disableDepthTestDistance = undefined;
            }
          }
          
          // 设置标签可见性 - 考虑全局设置
          if (entity.label) {
            // 只有当全局标签设置为可见时，才应用LOD特定的标签可见性
            const showLabel = this.labelsVisible && satellitesWithLabels.includes(satId);
            entity.label.show = showLabel;
            
            // 只有当标签应该显示时，才调整其样式
            if (showLabel) {
              if (detailLevel === 'low') {
                entity.label.font = '10px sans-serif';
                entity.label.pixelOffset = new Cesium.Cartesian2(10, 0);
              } else if (detailLevel === 'medium') {
                entity.label.font = '12px sans-serif';
                entity.label.pixelOffset = new Cesium.Cartesian2(12, 0);
              } else {
                entity.label.font = '14px sans-serif';
                entity.label.pixelOffset = new Cesium.Cartesian2(15, 0);
              }
            }
          }
          
          // 设置轨道可见性 - 考虑全局设置
          if (entity.path) {
            // 只有当全局轨道设置为可见时，才应用LOD特定的轨道可见性
            const showOrbit = this.orbitsVisible && satellitesWithOrbits.includes(satId);
            entity.path.show = showOrbit;
            
            // 只有当轨道应该显示时，才调整其样式
            if (showOrbit) {
              if (detailLevel === 'low') {
                entity.path.width = 0.5;
              } else if (detailLevel === 'medium') {
                entity.path.width = 1.0;
              } else {
                entity.path.width = 1.5;
              }
            }
          }
        });
        
        // 隐藏超出显示限制的卫星
        hiddenSatelliteIds.forEach(satId => {
          const entity = this.viewer.entities.getById(satId);
          if (!entity) return;
          
          entity.show = false;
        });
      } catch (lodError) {
        console.error('[LOD Update] 应用LOD设置时出错:', lodError);
        // 出错时尝试直接应用LOD
        this.applyLodToAllSatellites(allSatelliteIds, detailLevel);
      }
      
      // 强制刷新场景
      this.viewer.scene.requestRender();
      
    } catch (error) {
      console.error('[LOD Update] 更新卫星LOD时出错:', error);
    }
  }

  /**
   * 为所有卫星应用相同的LOD级别
   * @param satIds 卫星ID数组
   * @param detailLevel 详细程度
   * @param cullingVolume 视锥体裁剪体积
   */
  private applyLodToAllSatellites(satIds: string[], detailLevel: 'high' | 'medium' | 'low', cullingVolume?: Cesium.CullingVolume): void {
    try {
      console.log(`为 ${satIds.length} 颗卫星应用 ${detailLevel} 级别的LOD`);
      
      // 调试信息：检查cullingVolume参数类型
      console.log(`[LOD Update] Computed CullingVolume:`, cullingVolume);
      console.log(`[LOD Update] Type of CullingVolume: ${typeof cullingVolume}`);
      
      // 检查cullingVolume是否具有computeVisibility方法
      const containsPointMethod = cullingVolume ? 'computeVisibility' in cullingVolume : false;
      console.log(`[LOD Update] computed containsPoint method exists: ${containsPointMethod}`);
      
      satIds.forEach(satId => {
        const entity = this.viewer.entities.getById(satId);
        if (!entity) return;
        
        try {
          // 如果提供了视锥体，检查卫星是否在视锥体内
          if (cullingVolume && entity.position) {
            const position = entity.position.getValue(this.viewer.clock.currentTime);
            if (position) {
              try {
                // 使用包围球和computeVisibility检查可见性
                const boundingSphere = new Cesium.BoundingSphere(position, 1.0);
                const visibility = cullingVolume.computeVisibility(boundingSphere);
                
                // 如果在视锥体外，隐藏卫星
                if (visibility === Cesium.Intersect.OUTSIDE) {
                  entity.show = false;
                  return;
                }
              } catch (visibilityError) {
                console.error(`[Apply LOD] 检查卫星 ${satId} 可见性时出错:`, visibilityError);
                // 出错时默认显示卫星，避免卫星意外消失
              }
            }
          }
          
          // 设置卫星可见性
          entity.show = true;
          
          // 调整图标尺寸
          if (entity.billboard) {
            entity.billboard.show = true;
            
            // 根据详细程度调整图标大小
            if (detailLevel === 'low') {
              entity.billboard.scale = 0.2;
              // 对远距离卫星禁用pickable以减少拾取计算
              entity.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;
            } else if (detailLevel === 'medium') {
              entity.billboard.scale = 0.3;
              entity.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;
            } else {
              entity.billboard.scale = 0.4;
              entity.billboard.disableDepthTestDistance = undefined;
            }
          }
          
          // 调整标签 - 考虑全局设置
          if (entity.label) {
            // 根据全局标签设置和详细程度决定是否显示
            if (!this.labelsVisible) {
              // 如果全局设置为不可见，无论LOD如何都不显示标签
              entity.label.show = false;
            } else {
              // 如果全局设置为可见，则根据LOD决定
              if (detailLevel === 'low') {
                entity.label.show = false;
              } else {
                entity.label.show = true;
                
                // 根据详细程度调整字体大小
                if (detailLevel === 'medium') {
                  entity.label.font = '11px sans-serif';
                  entity.label.pixelOffset = new Cesium.Cartesian2(12, 0);
                } else {
                  entity.label.font = '14px sans-serif';
                  entity.label.pixelOffset = new Cesium.Cartesian2(15, 0);
                }
              }
            }
          }
          
          // 调整轨道线 - 考虑全局设置
          if (entity.path) {
            // 根据全局轨道设置和详细程度决定是否显示
            if (!this.orbitsVisible) {
              // 如果全局设置为不可见，无论LOD如何都不显示轨道
              entity.path.show = false;
            } else {
              // 如果全局设置为可见，则根据LOD决定
              if (detailLevel === 'low') {
                entity.path.show = false;
              } else {
                entity.path.show = true;
                
                // 根据详细程度调整轨道线宽度
                if (detailLevel === 'medium') {
                  entity.path.width = 1.0;
                } else {
                  entity.path.width = 1.5;
                }
              }
            }
          }
        } catch (entityError) {
          console.error(`[Apply LOD] 处理卫星 ${satId} 时出错:`, entityError);
        }
      });
      
      // 强制刷新场景
      this.viewer.scene.requestRender();
    } catch (error) {
      console.error('[Apply LOD] LOD更新错误:', error);
    }
  }

  /**
   * 获取相机到地球中心距离
   */
  private getCameraHeightAboveEllipsoid(): number {
    // 获取相机位置（宽度、高度、3D坐标）
    const cameraPosition = this.camera.position;
    
    // 计算到地球中心距离
    const ellipsoid = this.viewer.scene.globe.ellipsoid;
    const cartographic = ellipsoid.cartesianToCartographic(cameraPosition);
    
    // 获取高度（米）
    const height = cartographic.height;
    
    return height;
  }

  /**
   * 获取视锥显示状态
   */
  public getSensorsVisibility(): boolean {
    return this.sensorsVisible;
  }

  /**
   * 设置所有卫星视锥显示/隐藏
   * @param visible 是否可见
   */
  public setAllSensorsVisibility(visible: boolean): void {
    // 保存全局可见性状态
    this.sensorsVisible = visible;
    
    if (this.satelliteController) {
      // 由于我们已经完善了 SatelliteController.setSensorsVisibility 方法
      // 它会处理所有现有卫星的视锥显示/隐藏
      this.satelliteController.setSensorsVisibility(visible);
    }
    
    console.log(`已${visible ? '显示' : '隐藏'}所有卫星视锥`);
  }

  /**
   * 检查卫星数量是否超过阈值，并相应设置轨迹计算模式
   * @returns 是否处于限制轨迹模式（卫星数量 > 500）
   */
  public checkSatelliteCountAndSetMode(): boolean {
    const satelliteCount = this.satelliteController.getTrackedSatelliteCount();
    const shouldLimitTrajectory = satelliteCount > 500;
    
    // 只有在模式变化时才记录日志
    if (this.isLimitedTrajectoryMode !== shouldLimitTrajectory) {
      console.log(`卫星数量: ${satelliteCount}, ${shouldLimitTrajectory ? '启用' : '禁用'}轨迹限制模式`);
      this.isLimitedTrajectoryMode = shouldLimitTrajectory;
    }
    
    return this.isLimitedTrajectoryMode;
  }

  /**
   * 获取当前是否处于轨迹限制模式
   * @returns 是否因为卫星数量过多而限制轨迹计算
   */
  public isInLimitedTrajectoryMode(): boolean {
    return this.isLimitedTrajectoryMode;
  }

  /**
   * 选择单个卫星并显示其详细轨迹
   * @param satId 卫星ID
   * @returns 是否成功选择了卫星
   */
  public selectSatelliteForDetailedTrajectory(satId: string): boolean {
    // 获取卫星实体
    const satellite = this.satelliteController.getTrackedSatellite(satId);
    if (!satellite || !satellite.line1 || !satellite.line2) {
      console.warn(`无法为卫星 ${satId} 显示详细轨迹: 未找到卫星或TLE数据`);
      return false;
    }

    // 记录当前选中的卫星ID
    this.selectedSatelliteId = satId;
    
    // 重置其他卫星的轨迹显示
    const satelliteEntities = this.satelliteController.getAllSatellites();
    for (const id of satelliteEntities) {
      // 跳过当前选中的卫星
      if (id === satId) continue;
      
      const entity = this.satelliteController.getSatelliteEntity(id);
      if (entity && entity.path) {
        // 设置其他卫星为短轨迹模式
        entity.path.leadTime = new Cesium.ConstantProperty(10 * 60); // 10分钟
      }
    }
    
    // 为选中的卫星设置完整轨迹
    const entity = this.satelliteController.getSatelliteEntity(satId);
    if (entity && entity.path) {
      // 计算轨道周期
      const orbitalPeriod = this.calculateOrbitalPeriod(satellite);
      entity.path.leadTime = new Cesium.ConstantProperty(orbitalPeriod * 60); // 显示一个完整轨道周期
      
      console.log(`为卫星 ${satId} 显示完整轨道周期（${orbitalPeriod.toFixed(2)}分钟）的轨迹`);
      
      // 将视图聚焦到该卫星
      this.viewer.trackedEntity = entity;
    }
    
    return true;
  }

  /**
   * 计算卫星的轨道周期
   * @param satellite 卫星数据
   * @returns 轨道周期（分钟）
   */
  private calculateOrbitalPeriod(satellite: { line1: string, line2: string }): number {
    try {
      // 从TLE第二行解析平均运动（每天圈数）
      const tle2 = satellite.line2.trim();
      const meanMotionStr = tle2.substring(52, 63).trim();
      const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
      
      if (!isNaN(meanMotion) && meanMotion > 0) {
        // 转换为轨道周期（分钟）
        const orbitalPeriod = (24 * 60) / meanMotion;
        return orbitalPeriod;
      }
    } catch (error) {
      console.warn(`计算卫星轨道周期失败:`, error);
    }
    
    return 90; // 默认90分钟
  }

  /**
   * 清除当前选中的卫星，恢复所有卫星的默认轨迹显示模式
   */
  public clearSelectedSatellite(): void {
    // 如果没有选中的卫星，无需操作
    if (!this.selectedSatelliteId) return;
    
    // 重置视图跟踪
    this.viewer.trackedEntity = undefined;
    
    // 根据当前卫星数量决定轨迹模式
    const isLimitedMode = this.checkSatelliteCountAndSetMode();
    
    // 重置所有卫星的轨迹显示
    const satelliteEntities = this.satelliteController.getAllSatellites();
    for (const id of satelliteEntities) {
      const entity = this.satelliteController.getSatelliteEntity(id);
      const satellite = this.satelliteController.getTrackedSatellite(id);
      
      if (entity && entity.path && satellite) {
        if (isLimitedMode) {
          // 限制轨迹模式下设置为短轨迹
          entity.path.leadTime = new Cesium.ConstantProperty(10 * 60); // 10分钟
        } else {
          // 标准模式下设置为完整轨道周期
          const orbitalPeriod = this.calculateOrbitalPeriod(satellite);
          entity.path.leadTime = new Cesium.ConstantProperty(orbitalPeriod * 60);
        }
      }
    }
    
    console.log(`已清除选中的卫星 ${this.selectedSatelliteId}，恢复${isLimitedMode ? '短轨迹' : '标准轨迹'}模式`);
    this.selectedSatelliteId = null;
  }

  /**
   * 获取当前选中的卫星ID
   * @returns 当前选中的卫星ID，如果没有则返回null
   */
  public getSelectedSatelliteId(): string | null {
    return this.selectedSatelliteId;
  }

  /**
   * 直接使用TLE数据显示卫星，不调用后端API计算位置
   * @param data 卫星TLE数据和其他参数
   */
  displaySatellitesWithTle(data: {
    tleData: Array<{
      id: string;
      name: string;
      line1: string;
      line2: string;
      constellation?: string;
    }>;
    isFirstBatch: boolean;
    timeRange: {
      start: Date;
      end: Date;
    };
    keepCameraPosition?: boolean; // 新增参数：是否保持相机位置不变
    progressCallback?: (progress: number, message: string, batchAddedCount?: number) => void; // 修改：进度回调支持批次计数
    totalSatelliteCount?: number; // 新增：全部卫星总数，用于正确显示进度
  }): void {
    try {
      console.log(`直接使用TLE数据显示卫星: ${data.tleData.length}颗`);
      
      // 保存当前相机状态
      const cameraState = {
        position: this.camera.position.clone(),
        heading: this.camera.heading,
        pitch: this.camera.pitch,
        roll: this.camera.roll
      };
      
      // 获取当前控制器状态
      const controller = this.viewer.scene.screenSpaceCameraController;
      const wasEnabled = controller.enableInputs;
      
      // 在第一批数据时清除现有卫星
      if (data.isFirstBatch && this.satelliteController) {
        // 清除缓存数据和现有卫星
        this.satelliteDataCache.clear();
        this.lastOrbitUpdateTimes.clear();
        this.satelliteController.clearAll();
        this.lightSatelliteRenderer.removeAll();
        console.log('已清除所有现有卫星和轨道实体以及相关缓存数据');
      }
      
      if (!data.tleData || data.tleData.length === 0) {
        console.warn('没有有效的TLE数据可以显示');
        message.warning('没有有效的TLE数据可以显示');
        return;
      }
      
      // 获取时间范围
      const now = new Date();
      const startDate = data.timeRange?.start || now;
      const endDate = data.timeRange?.end || new Date(now.getTime() + 10 * 60 * 1000); // 默认10分钟
      
      // 使用进度回调而不是内部进度条
      
      // 卫星计数
      let addedSatelliteCount = 0;
      let processedCount = 0;
      
      // 处理每颗卫星
      const processSatellites = async () => {
        const batchSize = 20; // 每批并行处理的卫星数量
        const satellites = data.tleData;
        
        // 分批处理
        for (let batchStart = 0; batchStart < satellites.length; batchStart += batchSize) {
          const batchEnd = Math.min(batchStart + batchSize, satellites.length);
          const batch = satellites.slice(batchStart, batchEnd);
          
          // 静默处理批次，减少日志输出
          
          // 并行处理这一批卫星
          const promises = batch.map(async (satellite) => {
            try {
              // 创建卫星ID - 使用NORAD ID作为唯一标识
              const uniqueSatId = `SATELLITE-${satellite.id}`;
              
              // 确保TLE格式正确
              const line1 = satellite.line1.trim();
              const line2 = satellite.line2.trim();
              const formattedLine1 = line1.startsWith('1 ') ? line1 : `1 ${line1}`;
              const formattedLine2 = line2.startsWith('2 ') ? line2 : `2 ${line2}`;
              
              // 使用SatelliteController直接处理TLE数据
              const sampledPosition = await this.createSampledPositionFromTle(
                satellite.id,
                satellite.name,
                formattedLine1,
                formattedLine2,
                startDate,
                endDate
              );
              
              if (sampledPosition) {
                // 使用轻量级渲染器添加卫星点
                const currentPosition = sampledPosition.getValue(Cesium.JulianDate.fromDate(startDate));
                if (currentPosition) {
                  const lightSatelliteData: LightSatelliteData = {
                    id: uniqueSatId,
                    name: satellite.name || satellite.id,
                    position: currentPosition,
                    constellation: satellite.constellation || 'default',
                    color: Cesium.Color.WHITE, // 将由渲染器根据星座设置颜色
                    tle: {
                      line1: formattedLine1,
                      line2: formattedLine2
                    },
                    sampledPosition: sampledPosition
                  };
                  
                  // 添加到轻量级渲染器
                  this.lightSatelliteRenderer.addBatch([lightSatelliteData]);
                  
                  // 静默添加卫星，减少日志输出
                } else {
                  console.warn(`无法获取卫星 ${satellite.name} (${satellite.id}) 的当前位置`);
                }
                
                // 初始化缓存信息
                this.satelliteDataCache.set(uniqueSatId, {
                  lastCalculationTime: startDate,
                  nextCalculationTime: endDate,
                  calculating: false
                });
                
                addedSatelliteCount++;
              } else {
                console.warn(`无法为卫星 ${satellite.name} (${satellite.id}) 创建有效的位置数据`);
              }
            } catch (error) {
              console.error(`处理卫星 ${satellite.name} (${satellite.id}) 时出错:`, error);
            } finally {
              processedCount++;
              
              // 更新进度
              if (processedCount % 5 === 0 || processedCount === satellites.length) {
                const progress = Math.round((processedCount / satellites.length) * 100);
                if (data.progressCallback) {
                  // 渲染阶段占总进度的70%，从30%开始到100%
                  const renderProgress = 30 + (progress * 0.7);
                  const totalCount = data.totalSatelliteCount || satellites.length;
                  data.progressCallback(renderProgress, `正在渲染卫星 ${addedSatelliteCount}/${totalCount} 颗`);
                }
              }
            }
          });
          
          // 等待这批卫星全部处理完成
          await Promise.all(promises);
          
          // 强制刷新场景
          this.viewer.scene.requestRender();
          
          // 给UI一些刷新时间
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        // 所有卫星处理完成，仅保留关键日志
        console.log(`✅ 卫星加载完成: ${addedSatelliteCount}/${satellites.length} 颗`);
        
        // 启用自动位置更新机制
        if (addedSatelliteCount > 0) {
          this.setupClockListener();
          const currentTime = Cesium.JulianDate.toDate(this.viewer.clock.currentTime);
      this.checkAndUpdateSatellitePositions(currentTime);
          
          // 通过回调通知完成，传递当前批次添加的卫星数量
          if (data.progressCallback) {
            const totalCount = data.totalSatelliteCount || satellites.length;
            data.progressCallback(100, `成功渲染 ${addedSatelliteCount}/${totalCount} 颗卫星`, addedSatelliteCount);
          }
        } else {
          // 通过回调通知失败
          if (data.progressCallback) {
            data.progressCallback(100, '渲染失败，未能显示任何卫星', 0);
          }
        }
        
        // 如果需要保持相机位置，恢复相机状态
        if (data.keepCameraPosition) {
          // 恢复相机状态
          this.camera.setView({
            destination: cameraState.position,
            orientation: {
              heading: cameraState.heading,
              pitch: cameraState.pitch,
              roll: cameraState.roll
            }
          });
          
          console.log('已恢复相机位置');
        }
        
        // 恢复相机控制
        controller.enableInputs = wasEnabled;
        controller.enableZoom = true;
        controller.enableTranslate = true;
        controller.enableTilt = true;
        controller.enableLook = true;
        
        console.log('已恢复相机控制');
      };
      
      // 开始处理卫星
      processSatellites();
      
    } catch (error) {
      console.error('显示卫星时出错:', error);
      message.error('显示卫星时出错: ' + (error instanceof Error ? error.message : String(error)));
    }
  }
  
  /**
   * 使用TLE数据创建卫星采样位置
   * @param satId 卫星ID
   * @param satName 卫星名称
   * @param line1 TLE第一行
   * @param line2 TLE第二行
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 采样位置属性
   */
  private async createSampledPositionFromTle(
    satId: string,
    satName: string,
    line1: string,
    line2: string,
    startTime: Date,
    endTime: Date
  ): Promise<Cesium.SampledPositionProperty | null> {
    try {
      // 确保satellite.js库可用
      const satellite = await import('satellite.js');
      
      // 确保TLE格式正确
      const formattedLine1 = line1.trim().startsWith('1 ') ? line1.trim() : `1 ${line1.trim()}`;
      const formattedLine2 = line2.trim().startsWith('2 ') ? line2.trim() : `2 ${line2.trim()}`;
      
      console.log(`使用以下TLE数据为卫星 ${satName} (${satId}) 创建轨道:
      Line1: ${formattedLine1}
      Line2: ${formattedLine2}`);
      
      // 解析TLE数据
      const satrec = satellite.twoline2satrec(formattedLine1, formattedLine2);
      if (!satrec) {
        console.error(`无法从TLE数据创建卫星记录: ${formattedLine1}, ${formattedLine2}`);
        return null;
      }
      
      // 计算卫星轨道周期（分钟）
      const meanMotion = satrec.no * 60 * 24 / (2 * Math.PI); // 转换为每分钟圈数
      const periodMinutes = 1 / meanMotion;
      console.log(`卫星 ${satName} (${satId}) 轨道周期: ${periodMinutes.toFixed(2)}分钟`);
      
      // 创建采样位置属性
      const sampledPosition = new Cesium.SampledPositionProperty();
      
      // 计算更长时间轨道 - 至少60分钟或一个完整轨道周期
      const orbitTimeMinutes = Math.max(60, periodMinutes);
      const endTimeExtended = new Date(startTime.getTime() + orbitTimeMinutes * 60 * 1000);
      console.log(`扩展轨道计算时间至: ${orbitTimeMinutes.toFixed(1)}分钟`);
      
      // 使用足够的采样点确保轨道平滑
      const numPoints = 120; // 固定每个轨迹为120个点，确保足够平滑
      const timeSpanMs = endTimeExtended.getTime() - startTime.getTime();
      const intervalMs = timeSpanMs / numPoints;
      
      // 计算起始时间的Julian日期
      const startJulian = Cesium.JulianDate.fromDate(startTime);
      
      // 有效采样点计数
      let validPointCount = 0;
      
      // 添加采样点
      for (let i = 0; i <= numPoints; i++) {
        // 计算当前时间点
        const currentTime = new Date(startTime.getTime() + i * intervalMs);
        const julianDate = Cesium.JulianDate.addSeconds(
          startJulian, 
          i * (intervalMs / 1000), 
          new Cesium.JulianDate()
        );
        
        // 使用satellite.js计算卫星位置
        const positionAndVelocity = satellite.propagate(satrec, currentTime);
        if (!positionAndVelocity.position) {
          console.warn(`卫星 ${satName} 在时间点 ${currentTime.toISOString()} 的位置计算失败`);
          continue;
        }
        
        const { position } = positionAndVelocity;
        
        // 转换为笛卡尔坐标（千米到米）
        const positionCartesian = new Cesium.Cartesian3(
          position.x * 1000, 
          position.y * 1000, 
          position.z * 1000
        );
        
        // 验证位置有效性 - 确保不在地球内部
        const magnitude = Cesium.Cartesian3.magnitude(positionCartesian);
        const earthRadius = 6371000; // 地球半径(米)
        
        if (magnitude > earthRadius) {
          // 添加采样点
          sampledPosition.addSample(julianDate, positionCartesian);
          validPointCount++;
        } else {
          console.warn(`卫星 ${satName} 计算的位置在地球内部或无效: 距离地心 ${(magnitude/1000).toFixed(1)}km`);
        }
      }
      
      // 确保至少有3个有效点
      if (validPointCount < 3) {
        console.error(`卫星 ${satName} 的有效位置点不足，仅有 ${validPointCount} 个点`);
        return null;
      }
      
      // 设置插值器，使轨道曲线更平滑
      sampledPosition.setInterpolationOptions({
        interpolationDegree: 2,
        interpolationAlgorithm: Cesium.LagrangePolynomialApproximation
      });
      
      console.log(`为卫星 ${satName} (${satId}) 创建了 ${validPointCount} 个有效位置采样点`);
      return sampledPosition;
    } catch (error) {
      console.error(`为卫星 ${satName} (${satId}) 创建采样位置时出错:`, error);
      return null;
    }
  }

  /**
   * 设置所有卫星轨道的可见性
   * @param visible 是否可见
   */
  public setOrbitsVisible(visible: boolean): void {
    console.log(`设置所有卫星轨道${visible ? '可见' : '隐藏'}`);
    this.orbitsVisible = visible;
    
    // 更新所有卫星实体的轨道可见性
    const satelliteIds = this.satelliteController.getAllSatellites();
    let updatedCount = 0;
    
    satelliteIds.forEach(satId => {
      const entity = this.viewer.entities.getById(satId);
      if (entity && entity.path) {
        entity.path.show = visible;
        updatedCount++;
      }
    });
    
    // 同时更新直接创建的实体轨道可见性
    const directEntities = this.viewer.entities.values.filter(entity => 
      entity.id && entity.id.toString().startsWith('DIRECT-')
    );
    
    directEntities.forEach(entity => {
      if (entity.path) {
        entity.path.show = visible;
        updatedCount++;
      }
    });
    
    console.log(`已更新 ${updatedCount} 个轨道的可见性状态为 ${visible ? '可见' : '隐藏'}`);
    
    // 强制刷新场景
    this.viewer.scene.requestRender();
  }

  /**
   * 处理鼠标移动事件 - 已移至SatelliteController
   * @param position 鼠标位置
   */
  private handleMouseMove(position: Cesium.Cartesian2): void {
    // 悬停功能已移至SatelliteController中实现
    // 此方法保留以防其他功能需要
  }

  /**
   * 显示悬停时的轨道和标签 - 已移至SatelliteController
   * @param satelliteId 卫星ID
   */
  private showHoverDisplay(satelliteId: string): void {
    // 悬停功能已移至SatelliteController中实现
  }

  /**
   * 创建悬停时的标签
   * @param satelliteData 卫星数据
   */
  private createHoverLabel(satelliteData: LightSatelliteData): void {
    const labelId = `hover-label-${satelliteData.id}`;
    
    this.hoverLabelEntity = new Cesium.Entity({
      id: labelId,
      name: `${satelliteData.name} Label`,
      position: satelliteData.position,
      label: {
        text: satelliteData.name,
        font: '16px sans-serif',
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        fillColor: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        pixelOffset: new Cesium.Cartesian2(15, -15),
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        showBackground: true,
        backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
        backgroundPadding: new Cesium.Cartesian2(8, 4),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        show: true
      }
    });

    this.viewer.entities.add(this.hoverLabelEntity);
  }

  /**
   * 创建悬停时的轨道 - 修复版本，确保轨道从卫星实际位置开始
   * @param satelliteData 卫星数据
   */
  private async createHoverOrbit(satelliteData: LightSatelliteData): Promise<void> {
    if (!satelliteData.tle) {
      console.warn('卫星没有TLE数据，无法创建轨道');
      return;
    }

    const orbitId = `hover-orbit-${satelliteData.id}`;
    
    try {
      // 直接导入satellite.js进行SGP4计算
      const satellite = await import('satellite.js');
      
      // 计算轨道周期
      let orbitalPeriod = 90; // 默认90分钟
      try {
        const line2 = satelliteData.tle.line2.trim();
        const meanMotionStr = line2.substring(52, 63).trim();
        const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
        if (!isNaN(meanMotion) && meanMotion > 0) {
          orbitalPeriod = (24 * 60) / meanMotion; // 转换为分钟
          console.log(`卫星 ${satelliteData.name} 轨道周期: ${orbitalPeriod.toFixed(2)} 分钟`);
        }
      } catch (error) {
        console.warn('计算轨道周期失败:', error);
      }

      // 使用SGP4创建卫星记录
      const satrec = satellite.twoline2satrec(satelliteData.tle.line1, satelliteData.tle.line2);
      
      if (!satrec) {
        console.error('无法创建SGP4卫星记录');
        return;
      }

      console.log(`🚀 开始计算卫星 ${satelliteData.name} 的轨道，周期: ${orbitalPeriod.toFixed(2)} 分钟`);

      // 🌟 关键修复：直接使用卫星在lightSatelliteRenderer中的实际显示位置作为轨道起点
      let satelliteActualPosition: Cesium.Cartesian3 | null = null;
      
      // 首先尝试从lightSatelliteRenderer获取卫星实际位置
      if (this.lightSatelliteRenderer) {
        const lightSatData = this.lightSatelliteRenderer.getSatellite(satelliteData.id);
        if (lightSatData && lightSatData.position) {
          satelliteActualPosition = lightSatData.position;
          console.log(`✅ 从lightSatelliteRenderer获取卫星实际位置: (${satelliteActualPosition.x.toFixed(0)}, ${satelliteActualPosition.y.toFixed(0)}, ${satelliteActualPosition.z.toFixed(0)}) 米`);
        }
      }
      
      // 如果没有找到，使用传入的satelliteData位置
      if (!satelliteActualPosition && satelliteData.position) {
        satelliteActualPosition = satelliteData.position;
        console.log(`✅ 使用传入的卫星位置: (${satelliteActualPosition.x.toFixed(0)}, ${satelliteActualPosition.y.toFixed(0)}, ${satelliteActualPosition.z.toFixed(0)}) 米`);
      }
      
      // 如果还是没有找到，重新计算当前位置
      if (!satelliteActualPosition) {
        console.log(`📍 重新计算卫星当前位置作为轨道起点`);
        const currentTime = new Date();
      const currentPositionAndVelocity = satellite.propagate(satrec, currentTime);
      
      if (currentPositionAndVelocity.position && typeof currentPositionAndVelocity.position !== 'boolean') {
        const positionEci = currentPositionAndVelocity.position;
          satelliteActualPosition = new Cesium.Cartesian3(
          positionEci.x * 1000, // 转换为米
          positionEci.y * 1000,
          positionEci.z * 1000
        );
          console.log(`✅ 重新计算的卫星位置: (${satelliteActualPosition.x.toFixed(0)}, ${satelliteActualPosition.y.toFixed(0)}, ${satelliteActualPosition.z.toFixed(0)}) 米`);
      } else {
        console.error(`❌ 无法计算卫星当前位置`);
        return;
      }
      }

      // 🌟 关键修复：轨道计算从卫星实际位置开始，确保完全对齐
      const orbitPositions: Cesium.Cartesian3[] = [];
      const currentTime = new Date();
      
      // 第一个点就是卫星的实际显示位置
      orbitPositions.push(satelliteActualPosition);
      console.log(`🎯 轨道起点设置为卫星实际位置`);
      
      // 计算采样点数量 - 确保轨道平滑
      const sampleCount = Math.max(360, Math.ceil(orbitalPeriod * 4)); // 每15秒一个点，最少360个点
      const orbitalPeriodMs = orbitalPeriod * 60 * 1000;
      const sampleInterval = orbitalPeriodMs / sampleCount;
      
      console.log(`🔄 轨道采样参数: ${sampleCount} 个点, 间隔 ${(sampleInterval / 1000).toFixed(1)} 秒`);

      let successfulPoints = 1; // 已经有起点了

      // 🌟 关键修复：从当前时间开始，按照统一的时间步长计算轨道点
      // 这确保了轨道的运动方向与卫星的运动方向完全一致
      for (let i = 1; i <= sampleCount; i++) {
        const sampleTime = new Date(currentTime.getTime() + i * sampleInterval);
        
        try {
          // 使用SGP4传播器计算卫星位置
          const positionAndVelocity = satellite.propagate(satrec, sampleTime);
          
          if (positionAndVelocity.position && typeof positionAndVelocity.position !== 'boolean') {
            const positionEci = positionAndVelocity.position;
            
            // 使用ECI坐标，与卫星位置计算保持一致
            const eciCartesian = new Cesium.Cartesian3(
              positionEci.x * 1000, // 转换为米
              positionEci.y * 1000,
              positionEci.z * 1000
            );
            
            // 验证坐标有效性
            if (!isNaN(eciCartesian.x) && !isNaN(eciCartesian.y) && !isNaN(eciCartesian.z)) {
              orbitPositions.push(eciCartesian);
              successfulPoints++;
            }
          }
        } catch (error) {
          // 静默处理个别计算失败的点
          if (i % 100 === 0) {
            console.warn(`SGP4计算轨道点 ${i} 失败:`, error);
          }
        }
      }

      console.log(`✨ 轨道计算完成: ${successfulPoints}/${sampleCount + 1} 个有效位置点`);

      if (orbitPositions.length < 50) {
        console.warn(`无法计算卫星 ${satelliteData.name} 的轨道路径，有效点数不足: ${orbitPositions.length}`);
        return;
      }

      // 🌟 关键修复：确保轨道闭合 - 最后一个点回到起点附近
      if (orbitPositions.length > 0 && satelliteActualPosition) {
        orbitPositions.push(satelliteActualPosition); // 添加起点确保轨道闭合
        console.log(`🔄 轨道闭合：回到卫星实际位置`);
      }

      console.log(`🎯 创建轨道polyline，包含 ${orbitPositions.length} 个位置点`);

      // 🌟 关键修复：统一轨道线条样式，确保粗细一致
      this.hoverOrbitEntity = new Cesium.Entity({
        id: orbitId,
        name: `${satelliteData.name} Orbit`,
        polyline: {
          positions: orbitPositions,
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.4,
            color: Cesium.Color.YELLOW.withAlpha(0.9),
            taperPower: 0.2
          }),
          width: 4, // 🌟 统一使用4像素宽度，确保视觉效果一致
          clampToGround: false,
          show: true
        }
      });

      // 设置实体的参考坐标系为惯性坐标系
      (this.hoverOrbitEntity as any).referenceFrame = Cesium.ReferenceFrame.INERTIAL;

      // 添加到viewer
      this.viewer.entities.add(this.hoverOrbitEntity);
      
      // 强制刷新场景
      this.viewer.scene.requestRender();
      
      console.log(`🎉 轨道创建成功: ${satelliteData.name}`);
      console.log(`✅ 轨道实体已添加到viewer，实体ID: ${this.hoverOrbitEntity.id}`);
      console.log(`🔵 轨道包含 ${orbitPositions.length} 个位置点，从卫星实际位置开始`);
      console.log(`🌍 轨道运动方向与卫星运动方向完全一致，线条宽度统一为4像素`);
      
      // 验证实体是否真的被添加了
      const addedEntity = this.viewer.entities.getById(orbitId);
      if (addedEntity) {
        console.log(`✅ 确认轨道实体已成功添加到entities集合`);
      } else {
        console.error(`❌ 轨道实体未能添加到entities集合`);
      }
      
    } catch (error) {
      console.error(`❌ 创建轨道失败:`, error);
    }
  }

  /**
   * 清除悬停显示
   */
  private clearHoverDisplay(): void {
    // 移除悬停标签
    if (this.hoverLabelEntity) {
      this.viewer.entities.remove(this.hoverLabelEntity);
      this.hoverLabelEntity = null;
    }

    // 移除悬停轨道
    if (this.hoverOrbitEntity) {
      this.viewer.entities.remove(this.hoverOrbitEntity);
      this.hoverOrbitEntity = null;
    }

    // 重置悬停状态
    this.hoveredSatelliteId = null;
  }
} 