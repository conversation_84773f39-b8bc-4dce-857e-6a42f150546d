import React from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { WikiLaunchSiteInfo } from '../../../types/launchSite';

const Panel = styled.div`
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 24px;
  border-radius: 12px;
  width: 350px;
  max-height: 80vh;
  overflow-y: auto;
  color: white;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
`;

const TitleBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 12px;
`;

const Title = styled.h3`
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
`;

const InfoSection = styled.div`
  margin-bottom: 16px;
`;

const InfoItem = styled.div`
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
`;

const Label = styled.span`
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  min-width: 80px;
  font-size: 14px;
`;

const Value = styled.span`
  color: #ffffff;
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
`;

const Description = styled.div`
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.5;
  margin-top: 8px;
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
`;

const ActionButton = styled.button`
  flex: 1;
  padding: 10px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
  }

  &:active {
    transform: translateY(1px);
  }
`;

const PrimaryButton = styled(ActionButton)`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
`;

const LinkButton = styled.button`
  background: none;
  border: none;
  color: #4a9eff;
  text-decoration: underline;
  cursor: pointer;
  font-size: 12px;
  padding: 0;
  margin-top: 4px;

  &:hover {
    color: #66b3ff;
  }
`;

interface LaunchSiteWikiPanelProps {
  siteName: string;
  wikiInfo: WikiLaunchSiteInfo;
  onClose: () => void;
}

export const LaunchSiteWikiPanel: React.FC<LaunchSiteWikiPanelProps> = ({
  siteName,
  wikiInfo,
  onClose,
}) => {
  const navigate = useNavigate();

  const handleViewDetails = () => {
    navigate(`/launch-site/${encodeURIComponent(siteName)}`);
  };

  const handleOpenWikiSource = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <Panel>
      <TitleBar>
        <Title>{wikiInfo.site_name?.[0] || siteName}</Title>
        <CloseButton onClick={onClose} title="关闭">
          ×
        </CloseButton>
      </TitleBar>

      <InfoSection>
        <InfoItem>
          <Label>国家：</Label>
          <Value>{wikiInfo.country}</Value>
        </InfoItem>
        
        <InfoItem>
          <Label>位置：</Label>
          <Value>{wikiInfo.location}</Value>
        </InfoItem>
        
        <InfoItem>
          <Label>运营时间：</Label>
          <Value>{wikiInfo.operational_date}</Value>
        </InfoItem>
        
        <InfoItem>
          <Label>发射次数：</Label>
          <Value>{wikiInfo.launch_num}</Value>
        </InfoItem>
        
        <InfoItem>
          <Label>最重载荷：</Label>
          <Value>{wikiInfo.launch_heaviest}</Value>
        </InfoItem>
        
        <InfoItem>
          <Label>最高轨道：</Label>
          <Value>{wikiInfo.launch_highest}</Value>
        </InfoItem>
        
        {wikiInfo.notes && (
          <InfoItem>
            <Label>备注：</Label>
            <Description>{wikiInfo.notes}</Description>
          </InfoItem>
        )}
        
        {wikiInfo.info_source && wikiInfo.info_source.length > 0 && (
          <InfoItem>
            <Label>信息来源：</Label>
            <div>
              {wikiInfo.info_source.map((url, index) => (
                <LinkButton
                  key={index}
                  onClick={() => handleOpenWikiSource(url)}
                  title={`打开 ${url}`}
                >
                  Wikipedia 链接 {index + 1}
                </LinkButton>
              ))}
            </div>
          </InfoItem>
        )}
      </InfoSection>

      <ButtonContainer>
        <ActionButton onClick={onClose}>
          关闭
        </ActionButton>
        <PrimaryButton onClick={handleViewDetails}>
          查看详情
        </PrimaryButton>
      </ButtonContainer>
    </Panel>
  );
};
