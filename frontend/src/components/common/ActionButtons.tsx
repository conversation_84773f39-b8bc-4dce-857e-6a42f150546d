import React from 'react';
import { useParams } from 'react-router-dom';
import { useSatellite } from '../../hooks/useSatellite';
import { useDebris } from '../../hooks/useDebris';
import { useLaunchSite } from '../../hooks/useLaunchSite';
import { useSatelliteStore } from '../../store/satellite';
import { SatelliteData } from '../search/results/SatelliteSearchResults';

interface ActionButtonsProps {
  id?: string;
  type?: 'satellite' | 'debris' | 'launchsite' | 'launchprovider';
}

export function ActionButtons({ id, type }: ActionButtonsProps) {
  const params = useParams();
  const actualId = id || params.id;
  const actualType = type || params.type;
  
  // 获取对应类型的数据
  const { satellite } = useSatellite(actualType === 'satellite' ? actualId : undefined);
  const { debris } = useDebris(actualType === 'debris' ? actualId : undefined);
  const { launchSite } = useLaunchSite(actualType === 'launchsite' ? actualId : undefined);
  
  // 使用关注状态管理
  const { toggleFollowSatellite, isFollowed } = useSatelliteStore();
  const isCurrentlyFollowed = actualType === 'satellite' && actualId ? isFollowed(actualId) : false;

  const handleSituationClick = () => {
    console.log('查看态势:', { type: actualType, id: actualId });
    
    const params = new URLSearchParams();
    params.set('type', actualType || '');
    params.set('id', actualId || '');

    // 根据类型添加不同的参数
    if (actualType === 'satellite') {
      // 尝试从localStorage获取卫星数据
      try {
        const cachedData = localStorage.getItem('current_satellite_data');
        if (cachedData) {
          const satelliteData = JSON.parse(cachedData) as SatelliteData;
          // 提取NORAD ID
          if (satelliteData.norad_id && satelliteData.norad_id.length > 0) {
            const noradId = satelliteData.norad_id[0].value;
            params.set('noradId', noradId.toString());
            console.log('从localStorage获取到NORAD ID:', noradId);
          } else {
            console.warn('卫星数据中未找到NORAD ID');
          }
        } else {
          console.warn('localStorage中未找到卫星数据');
        }
      } catch (error) {
        console.error('解析localStorage中的卫星数据失败:', error);
      }
      
      // 如果没有从localStorage获取到NORAD ID，尝试使用原有的TLE数据方式作为备用
      if (!params.get('noradId') && satellite?.tle) {
        params.set('tle1', satellite.tle.line1);
        params.set('tle2', satellite.tle.line2);
        console.log('使用备用TLE数据方式');
      }
    } else if (actualType === 'debris' && debris?.tle) {
      params.set('tle1', debris.tle.line1);
      params.set('tle2', debris.tle.line2);
    } else if (actualType === 'launchsite' && launchSite?.position) {
      params.set('longitude', launchSite.position.longitude.toString());
      params.set('latitude', launchSite.position.latitude.toString());
      params.set('altitude', launchSite.position.altitude.toString());
    }

    // 发射服务商类型不支持态势查看，只显示分享和关注按钮
    if (actualType !== 'launchprovider') {
      // 打开态势页面
      window.open(`/space-situation?${params.toString()}`, '_blank');
    }
  };
  
  // 处理关注点击
  const handleFollowClick = () => {
    if (actualType === 'satellite' && actualId) {
      toggleFollowSatellite(actualId);
    }
  };

  return (
    <div className="flex items-center space-x-3">
      {/* 态势按钮 - 除了发射服务商外的其他类型才显示 */}
      {actualType !== 'launchprovider' && (
        <button 
          onClick={handleSituationClick}
          className="p-2 rounded-full bg-blue-500/20 hover:bg-blue-500/30 transition-colors group"
          title="查看态势"
        >
          <svg className="w-5 h-5 text-blue-400 group-hover:text-blue-300" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
      )}

      {/* 分享按钮 */}
      <button className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors group">
        <svg className="w-5 h-5 text-white/70 group-hover:text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
        </svg>
      </button>

      {/* 关注按钮 - 只有卫星类型显示功能按钮，其他类型为禁用状态 */}
      <button 
        className={`p-2 rounded-full transition-colors group ${
          actualType === 'satellite' 
            ? isCurrentlyFollowed 
              ? 'bg-red-500/70 hover:bg-red-500/90'
              : 'bg-white/10 hover:bg-red-500/30'
            : 'bg-white/10 opacity-50 cursor-not-allowed'
        }`}
        onClick={handleFollowClick}
        disabled={actualType !== 'satellite'}
        title={isCurrentlyFollowed ? "取消关注" : "关注此卫星"}
      >
        <svg className={`w-5 h-5 ${
          actualType === 'satellite' 
            ? isCurrentlyFollowed 
              ? 'text-white'
              : 'text-white/70 group-hover:text-red-500'
            : 'text-white/40'
        }`} 
          viewBox="0 0 24 24" 
          fill={isCurrentlyFollowed ? "currentColor" : "none"} 
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      </button>
    </div>
  );
} 