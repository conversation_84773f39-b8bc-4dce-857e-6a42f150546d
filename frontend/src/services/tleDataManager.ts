/**
 * TLE数据管理服务
 * 负责定时请求API获取TLE数据，并管理本地数据缓存
 */

import { apiService } from './apiService';
import { simpleIndexedDB, TleDataCache, TleDataItem } from '../utils/simpleIndexedDB';
import { multilayerCacheManager } from '../utils/multilayerCacheManager';

export interface TleApiResponse {
  success: boolean;
  total: number;
  executionTime: number;
  timeRangeStart: string;
  timeRangeEnd: number;
  timeRangeMinutes: number;
  method: string;
  queryStrategy: string;
  sampleMode: boolean;
  results: TleDataItem[];
}

export interface TleDataStatus {
  hasData: boolean;
  isExpired: boolean;
  lastUpdated: string | null;
  nextUpdate: string | null;
  satelliteCount: number;
  dataAge: string;
  isUpdating: boolean;
  lastError: string | null;
}

class TleDataManager {
  private updateInterval: NodeJS.Timeout | null = null;
  private readonly UPDATE_INTERVAL_MS = 4 * 60 * 60 * 1000; // 4小时
  private readonly RETRY_INTERVAL_MS = 30 * 60 * 1000; // 30分钟重试间隔
  private readonly MAX_RETRIES = 3; // 最大重试次数
  
  private isUpdating = false;
  private lastError: string | null = null;
  private retryCount = 0;
  private retryTimeout: NodeJS.Timeout | null = null;

  // 事件监听器
  private listeners: {
    onUpdateStart?: () => void;
    onUpdateProgress?: (progress: number, message: string) => void;
    onUpdateComplete?: (data: TleDataCache) => void;
    onUpdateError?: (error: string) => void;
    onStatusChange?: (status: TleDataStatus) => void;
  } = {};

  /**
   * 初始化TLE数据管理器
   */
  async init(): Promise<void> {
    console.log('🚀 初始化TLE数据管理器');
    
    try {
      // 初始化IndexedDB
      await simpleIndexedDB.init();

      // 检查本地数据状态
      const cachedData = await simpleIndexedDB.getTleData();
      const status = this.getDataStatus(cachedData);
      
      console.log('📊 TLE数据状态:', status);
      
      // 如果没有数据或数据过期，立即获取一次
      if (!status.hasData || status.isExpired) {
        console.log('📡 本地数据不存在或已过期，立即获取TLE数据');
        await this.updateTleData();
      } else {
        console.log('✅ 本地数据有效，使用缓存数据');
      }
      
      // 启动定时更新
      this.startPeriodicUpdate();
      
      console.log('🎉 TLE数据管理器初始化完成');
    } catch (error) {
      console.error('❌ TLE数据管理器初始化失败:', error);
      this.lastError = error instanceof Error ? error.message : String(error);
      this.notifyStatusChange();
    }
  }

  /**
   * 启动定时更新
   */
  private startPeriodicUpdate(): void {
    // 清除现有定时器
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    // 设置新的定时器
    this.updateInterval = setInterval(async () => {
      console.log('⏰ 定时更新TLE数据');
      await this.updateTleData();
    }, this.UPDATE_INTERVAL_MS);

    console.log(`⏱️ 已设置定时更新，间隔：${this.UPDATE_INTERVAL_MS / 1000 / 60 / 60}小时`);
  }

  /**
   * 更新TLE数据
   */
  async updateTleData(force: boolean = false): Promise<TleDataCache | null> {
    if (this.isUpdating && !force) {
      console.log('⚠️ 正在更新中，跳过本次更新');
      return null;
    }

    console.log('🚀 updateTleData 开始执行，force =', force);
    this.isUpdating = true;
    this.lastError = null;
    console.log('📢 调用 notifyUpdateStart');
    this.notifyUpdateStart();
    console.log('📢 调用 notifyStatusChange (开始)');
    this.notifyStatusChange();

    try {
      console.log('📡 开始获取TLE数据...');
      console.log('📢 调用 notifyUpdateProgress(10)');
      this.notifyUpdateProgress(10, '正在请求API...');

      // 请求API获取TLE数据
      console.log('🌐 即将调用 apiService.post');
      const response = await apiService.post<TleApiResponse>(
        '/orbit/bulk-tle/all?sampleMode=false',
        {}
      );
      console.log('🌐 apiService.post 调用完成，响应:', {
        success: response.success,
        total: response.total,
        resultsLength: response.results?.length
      });

      console.log('📢 调用 notifyUpdateProgress(50)');
      this.notifyUpdateProgress(50, '正在处理数据...');

      if (!response.success || !response.results) {
        console.error('❌ API响应验证失败:', { success: response.success, hasResults: !!response.results });
        throw new Error(`API请求失败: ${JSON.stringify(response)}`);
      }

      console.log(`✅ API请求成功，获取到 ${response.results.length} 颗卫星数据`);

      // 验证和过滤TLE数据
      console.log('🔍 开始验证TLE数据...');
      const validTleData = response.results.filter(item => {
        // 检查必要字段
        if (!item || typeof item !== 'object') {
          console.warn('⚠️ 无效的数据项:', item);
          return false;
        }

        if (!item.norad_id || typeof item.norad_id !== 'number') {
          console.warn('⚠️ 缺少有效的norad_id:', item);
          return false;
        }

        if (!item.tle_raw || typeof item.tle_raw !== 'string') {
          console.warn('⚠️ 缺少有效的tle_raw:', item);
          return false;
        }

        // 检查TLE格式
        const tleLines = item.tle_raw.split('\n');
        let hasLine1 = false, hasLine2 = false;

        for (const line of tleLines) {
          const trimmedLine = line.trim();
          if (trimmedLine.startsWith('1 ')) {
            hasLine1 = true;
          } else if (trimmedLine.startsWith('2 ')) {
            hasLine2 = true;
          }
        }

        if (!hasLine1 || !hasLine2) {
          console.warn('⚠️ TLE数据格式无效:', item);
          return false;
        }

        return true;
      });

      console.log(`✅ 数据验证完成，有效数据: ${validTleData.length}/${response.results.length}`);

      // 准备缓存数据
      console.log('📦 开始准备缓存数据...');
      const now = new Date();
      const nextUpdate = new Date(now.getTime() + this.UPDATE_INTERVAL_MS);

      const cacheData: TleDataCache = {
        id: 'tle-cache',
        lastUpdated: now.toISOString(),
        nextUpdate: nextUpdate.toISOString(),
        total: validTleData.length,
        apiResponse: {
          success: response.success,
          total: response.total,
          executionTime: response.executionTime,
          timeRangeStart: response.timeRangeStart,
          timeRangeEnd: response.timeRangeEnd,
          timeRangeMinutes: response.timeRangeMinutes,
          method: response.method,
          queryStrategy: response.queryStrategy,
          sampleMode: response.sampleMode
        },
        data: validTleData
      };
      console.log('📦 缓存数据准备完成，数据大小:', JSON.stringify(cacheData).length, '字符');

      console.log('📢 调用 notifyUpdateProgress(80)');
      this.notifyUpdateProgress(80, '正在保存到本地...');

      // 保存到IndexedDB
      console.log('💾 开始保存到多层缓存系统...');
      
      // 检查数据大小
      const dataSizeInMB = JSON.stringify(cacheData).length / (1024 * 1024);
      console.log(`📊 数据大小: ${dataSizeInMB.toFixed(2)} MB`);
      
      try {
        console.log('💾 开始多层缓存保存操作...');
        
        // 使用多层缓存管理器保存数据
        // 它会自动尝试所有可用的存储层：内存、localStorage、sessionStorage、IndexedDB
        await multilayerCacheManager.saveTleData(cacheData);
        console.log('💾 多层缓存保存成功');
      } catch (saveError) {
        console.warn('💾 多层缓存保存失败:', saveError);
        console.warn('💾 错误详情:', {
          name: saveError instanceof Error ? saveError.name : 'Unknown',
          message: saveError instanceof Error ? saveError.message : String(saveError),
          stack: saveError instanceof Error ? saveError.stack : undefined
        });
        
        console.warn('⚠️ 所有存储层都保存失败，数据将仅在当前会话中使用');
      }

      console.log('📢 调用 notifyUpdateProgress(100)');
      this.notifyUpdateProgress(100, '更新完成');

      console.log(`🎉 TLE数据更新完成，共 ${cacheData.total} 颗卫星`);
      
      // 重置重试计数
      console.log('�� 重置重试计数...');
      this.retryCount = 0;
      if (this.retryTimeout) {
        clearTimeout(this.retryTimeout);
        this.retryTimeout = null;
      }

      console.log('📢 即将调用 notifyUpdateComplete 回调');
      this.notifyUpdateComplete(cacheData);
      console.log('📢 notifyUpdateComplete 调用完成');
      console.log('🎯 updateTleData 即将返回成功结果');
      return cacheData;

    } catch (error) {
      console.error('❌ 更新TLE数据失败:', error);
      this.lastError = error instanceof Error ? error.message : String(error);
      console.log('📢 即将调用 notifyUpdateError 回调');
      this.notifyUpdateError(this.lastError);
      
      // 重试机制
      console.log('🔄 调用 scheduleRetry...');
      this.scheduleRetry();
      
      console.log('🎯 updateTleData 即将返回 null (错误)');
      return null;
    } finally {
      console.log('📢 设置 isUpdating = false，即将调用 notifyStatusChange');
      this.isUpdating = false;
      this.notifyStatusChange();
      console.log('🏁 updateTleData finally 块执行完成');
    }
  }

  /**
   * 安排重试
   */
  private scheduleRetry(): void {
    if (this.retryCount >= this.MAX_RETRIES) {
      console.log(`❌ 已达到最大重试次数 (${this.MAX_RETRIES})，停止重试`);
      return;
    }

    this.retryCount++;
    const retryDelay = this.RETRY_INTERVAL_MS * Math.pow(2, this.retryCount - 1); // 指数退避

    console.log(`🔄 安排第 ${this.retryCount} 次重试，延迟 ${retryDelay / 1000 / 60} 分钟`);

    this.retryTimeout = setTimeout(async () => {
      console.log(`🔄 执行第 ${this.retryCount} 次重试`);
      await this.updateTleData();
    }, retryDelay);
  }

  /**
   * 获取TLE数据（优先使用缓存）
   */
  async getTleData(): Promise<TleDataItem[]> {
    try {
      console.log('🔍 开始获取TLE数据（优先使用缓存）');
      const cachedData = await multilayerCacheManager.getTleData();

      console.log('📊 缓存数据检查结果:', {
        hasCachedData: !!cachedData,
        hasDataArray: !!(cachedData?.data),
        dataLength: cachedData?.data?.length || 0,
        lastUpdated: cachedData?.lastUpdated,
        cacheTotal: cachedData?.total
      });

      if (cachedData && cachedData.data && cachedData.data.length > 0) {
        console.log(`✅ 使用多层缓存的TLE数据，共 ${cachedData.data.length} 颗卫星`);
        return cachedData.data;
      } else {
        console.log('⚠️ 没有有效的缓存数据，尝试立即获取');
        const freshData = await this.updateTleData(true);
        return freshData?.data || [];
      }
    } catch (error) {
      console.error('❌ 获取TLE数据失败:', error);
      return [];
    }
  }

  /**
   * 获取数据状态
   */
  getDataStatus(cachedData?: TleDataCache | null): TleDataStatus {
    // 如果没有提供缓存数据，尝试从多层缓存中获取
    if (!cachedData) {
      // 这里使用同步方式，如果没有内存缓存就返回基本状态
      const memoryCache = (multilayerCacheManager as any).memoryCache.get('tle-cache');
      cachedData = memoryCache || null;
    }
    
    const dbStatus = simpleIndexedDB.getDataStatus(cachedData || null);

    return {
      ...dbStatus,
      isUpdating: this.isUpdating,
      lastError: this.lastError
    };
  }

  /**
   * 手动刷新数据
   */
  async refreshData(): Promise<TleDataCache | null> {
    console.log('🔄 手动刷新TLE数据');
    return await this.updateTleData(true);
  }

  /**
   * 清除缓存数据
   */
  async clearCache(): Promise<void> {
    console.log('🗑️ 清除所有层级的TLE缓存数据');
    await multilayerCacheManager.clearAllCache();
    this.notifyStatusChange();
  }

  /**
   * 设置事件监听器
   */
  setListeners(listeners: Partial<typeof this.listeners>): void {
    this.listeners = { ...this.listeners, ...listeners };
  }

  /**
   * 通知更新开始
   */
  private notifyUpdateStart(): void {
    this.listeners.onUpdateStart?.();
  }

  /**
   * 通知更新进度
   */
  private notifyUpdateProgress(progress: number, message: string): void {
    this.listeners.onUpdateProgress?.(progress, message);
  }

  /**
   * 通知更新完成
   */
  private notifyUpdateComplete(data: TleDataCache): void {
    this.listeners.onUpdateComplete?.(data);
  }

  /**
   * 通知更新错误
   */
  private notifyUpdateError(error: string): void {
    this.listeners.onUpdateError?.(error);
  }

  /**
   * 通知状态变化
   */
  private notifyStatusChange(): void {
    // 使用同步方式获取状态，避免异步问题
    try {
      console.log('📢 notifyStatusChange 被调用，isUpdating =', this.isUpdating);
      const status = this.getDataStatus(null); // 先用 null 获取基本状态
      console.log('📢 当前状态:', status);
      this.listeners.onStatusChange?.(status);
      
      // 异步获取完整状态（不阻塞主流程）
      multilayerCacheManager.getTleData().then(cachedData => {
        const fullStatus = this.getDataStatus(cachedData);
        console.log('📢 完整状态:', fullStatus);
        this.listeners.onStatusChange?.(fullStatus);
      }).catch(error => {
        console.error('获取数据状态失败:', error);
      });
    } catch (error) {
      console.error('notifyStatusChange 执行失败:', error);
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    console.log('🔚 销毁TLE数据管理器');
    
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
      this.retryTimeout = null;
    }
    
    simpleIndexedDB.close();
  }
}

// 导出单例实例
export const tleDataManager = new TleDataManager(); 