/**
 * 星座数据管理服务
 * 负责定时请求API获取星座数据，并管理本地数据缓存
 * 采用与TLE数据管理器相同的缓存策略
 */

import { apiService } from './apiService';
import { simpleIndexedDB } from '../utils/simpleIndexedDB';

// 星座信息接口
export interface ConstellationInfo {
  name: string;
  satelliteCount: number;
}

// 星座数据缓存接口
export interface ConstellationDataCache {
  id: string;
  lastUpdated: string;
  nextUpdate: string;
  constellations: ConstellationInfo[];
  satelliteMapping: Record<string, number[]>;
  totalConstellations: number;
  totalSatellites: number;
}

// 星座下的卫星信息
export interface ConstellationSatellite {
  id: number;
  satellite_name: Array<{value: string, sources: string[]}>;
  alternative_name: Array<{value: string, sources: string[]}>;
  norad_id: Array<{value: number, sources: string[]}>;
  cospar_id: Array<{value: string, sources: string[]}>;
  constellation: Array<{value: string, sources: string[]}>;
}

// 星座数据状态
export interface ConstellationDataStatus {
  hasData: boolean;
  isExpired: boolean;
  lastUpdated: string | null;
  nextUpdate: string | null;
  constellationCount: number;
  satelliteCount: number;
  dataAge: string;
  isUpdating: boolean;
  lastError: string | null;
}

// API响应接口
interface ConstellationWithTleResponse {
  constellations: ConstellationInfo[];
}

interface SatelliteListResponse {
  success: boolean;
  total: number;
  results: ConstellationSatellite[];
}

class ConstellationDataManager {
  private updateInterval: NodeJS.Timeout | null = null;
  private readonly UPDATE_INTERVAL_MS = 12 * 60 * 60 * 1000; // 12小时
  private readonly RETRY_INTERVAL_MS = 30 * 60 * 1000; // 30分钟重试间隔
  private readonly MAX_RETRIES = 3; // 最大重试次数
  
  private isUpdating = false;
  private lastError: string | null = null;
  private retryCount = 0;
  private retryTimeout: NodeJS.Timeout | null = null;

  // 事件监听器
  private listeners: {
    onUpdateStart?: () => void;
    onUpdateProgress?: (progress: number, message: string) => void;
    onUpdateComplete?: (data: ConstellationDataCache) => void;
    onUpdateError?: (error: string) => void;
    onStatusChange?: (status: ConstellationDataStatus) => void;
  } = {};

  /**
   * 初始化星座数据管理器
   */
  async init(): Promise<void> {
    console.log('🚀 初始化星座数据管理器');
    
    try {
      // 初始化IndexedDB
      await simpleIndexedDB.init();

      // 检查本地数据状态
      const cachedData = await simpleIndexedDB.getConstellationData();
      const status = this.getDataStatus(cachedData);
      
      console.log('📊 星座数据状态:', status);
      
      // 如果没有数据或数据过期，立即获取一次
      if (!status.hasData || status.isExpired) {
        console.log('📡 本地星座数据不存在或已过期，立即获取数据');
        await this.updateConstellationData();
      } else {
        console.log('✅ 本地星座数据有效，使用缓存数据');
      }
      
      // 启动定时更新
      this.startPeriodicUpdate();
      
      console.log('🎉 星座数据管理器初始化完成');
    } catch (error) {
      console.error('❌ 星座数据管理器初始化失败:', error);
      this.lastError = error instanceof Error ? error.message : String(error);
      this.notifyStatusChange();
    }
  }

  /**
   * 启动定时更新
   */
  private startPeriodicUpdate(): void {
    // 清除现有的定时器
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    // 设置新的定时器，每12小时更新一次
    this.updateInterval = setInterval(async () => {
      console.log('⏰ 定时更新星座数据');
      await this.updateConstellationData();
    }, this.UPDATE_INTERVAL_MS);

    console.log(`⏱️ 已设置星座数据定时更新，间隔：${this.UPDATE_INTERVAL_MS / 1000 / 60 / 60}小时`);
  }

  /**
   * 更新星座数据
   */
  async updateConstellationData(force: boolean = false): Promise<ConstellationDataCache | null> {
    if (this.isUpdating && !force) {
      console.log('⚠️ 正在更新中，跳过本次更新');
      return null;
    }

    this.isUpdating = true;
    this.lastError = null;
    this.notifyUpdateStart();
    this.notifyStatusChange();

    try {
      console.log('📡 开始获取星座数据...');
      this.notifyUpdateProgress(10, '正在请求星座列表API...');

      // 1. 获取星座名称列表
      const constellationResponse = await apiService.get<ConstellationWithTleResponse>('/local/constellation/with-tle');
      
      if (!constellationResponse.constellations || constellationResponse.constellations.length === 0) {
        throw new Error('API返回的星座列表为空');
      }

      console.log(`✅ 获取到 ${constellationResponse.constellations.length} 个星座`);
      this.notifyUpdateProgress(30, '正在获取星座卫星数据...');

      // 2. 获取每个星座下的卫星NORAD ID
      const satelliteMapping: Record<string, number[]> = {};
      let totalSatellites = 0;
      let processedConstellations = 0;

      for (const constellation of constellationResponse.constellations) {
        try {
          console.log(`📡 获取星座 "${constellation.name}" 的卫星数据...`);
          
          // 获取星座下的所有卫星
          let allSatellites: ConstellationSatellite[] = [];
          let currentPage = 1;
          let hasMorePages = true;
          
          while (hasMorePages) {
            const satelliteResponse = await apiService.post<SatelliteListResponse>(
              '/api/v1/database/filter-satellites',
              {
                constellationName: constellation.name,
                hasTleData: true, // 只获取有TLE数据的卫星
                page: currentPage,
                limit: 100
              }
            );
            
            if (satelliteResponse.success && satelliteResponse.results.length > 0) {
              allSatellites = [...allSatellites, ...satelliteResponse.results];
              
              // 检查是否还有更多页
              if (allSatellites.length >= satelliteResponse.total || satelliteResponse.results.length < 100) {
                hasMorePages = false;
              } else {
                currentPage++;
              }
            } else {
              hasMorePages = false;
            }
          }

          // 提取NORAD ID
          const noradIds = allSatellites
            .map(sat => sat.norad_id?.[0]?.value)
            .filter((id): id is number => typeof id === 'number' && id > 0);

          satelliteMapping[constellation.name] = noradIds;
          totalSatellites += noradIds.length;
          
          console.log(`✅ 星座 "${constellation.name}" 包含 ${noradIds.length} 颗卫星`);
          
          processedConstellations++;
          const progress = 30 + Math.floor((processedConstellations / constellationResponse.constellations.length) * 60);
          this.notifyUpdateProgress(progress, `已处理 ${processedConstellations}/${constellationResponse.constellations.length} 个星座`);
          
        } catch (error) {
          console.error(`❌ 获取星座 "${constellation.name}" 的卫星数据失败:`, error);
          // 继续处理其他星座，不中断整个流程
          satelliteMapping[constellation.name] = [];
        }
      }

      this.notifyUpdateProgress(95, '正在保存到本地缓存...');

      // 3. 准备缓存数据
      const now = new Date();
      const nextUpdate = new Date(now.getTime() + this.UPDATE_INTERVAL_MS);

      const cacheData: ConstellationDataCache = {
        id: 'constellation-cache',
        lastUpdated: now.toISOString(),
        nextUpdate: nextUpdate.toISOString(),
        constellations: constellationResponse.constellations,
        satelliteMapping,
        totalConstellations: constellationResponse.constellations.length,
        totalSatellites
      };

      // 4. 保存到IndexedDB
      await simpleIndexedDB.saveConstellationData(cacheData);

      this.notifyUpdateProgress(100, '更新完成');

      console.log(`🎉 星座数据更新完成，共 ${cacheData.totalConstellations} 个星座，${cacheData.totalSatellites} 颗卫星`);
      
      // 重置重试计数
      this.retryCount = 0;
      if (this.retryTimeout) {
        clearTimeout(this.retryTimeout);
        this.retryTimeout = null;
      }

      this.notifyUpdateComplete(cacheData);
      return cacheData;

    } catch (error) {
      console.error('❌ 更新星座数据失败:', error);
      this.lastError = error instanceof Error ? error.message : String(error);
      this.notifyUpdateError(this.lastError);
      
      // 重试机制
      this.scheduleRetry();
      
      return null;
    } finally {
      this.isUpdating = false;
      this.notifyStatusChange();
    }
  }

  /**
   * 安排重试
   */
  private scheduleRetry(): void {
    if (this.retryCount >= this.MAX_RETRIES) {
      console.log(`❌ 已达到最大重试次数 (${this.MAX_RETRIES})，停止重试`);
      return;
    }

    this.retryCount++;
    const retryDelay = this.RETRY_INTERVAL_MS * Math.pow(2, this.retryCount - 1); // 指数退避
    
    console.log(`🔄 安排第 ${this.retryCount} 次重试，${Math.round(retryDelay / 1000 / 60)} 分钟后执行`);
    
    this.retryTimeout = setTimeout(() => {
      this.updateConstellationData();
    }, retryDelay);
  }

  /**
   * 获取星座数据（优先使用缓存）
   */
  async getConstellationData(): Promise<ConstellationDataCache | null> {
    try {
      const cachedData = await simpleIndexedDB.getConstellationData();
      
      if (cachedData) {
        console.log(`📦 使用缓存的星座数据，共 ${cachedData.totalConstellations} 个星座`);
        return cachedData;
      } else {
        console.log('⚠️ 没有缓存数据，尝试立即获取');
        const freshData = await this.updateConstellationData(true);
        return freshData;
      }
    } catch (error) {
      console.error('❌ 获取星座数据失败:', error);
      return null;
    }
  }

  /**
   * 获取星座列表
   */
  async getConstellationList(): Promise<ConstellationInfo[]> {
    const data = await this.getConstellationData();
    return data?.constellations || [];
  }

  /**
   * 获取指定星座的卫星NORAD ID列表
   */
  async getConstellationSatelliteIds(constellationName: string): Promise<number[]> {
    const data = await this.getConstellationData();
    return data?.satelliteMapping[constellationName] || [];
  }

  /**
   * 获取数据状态
   */
  getDataStatus(data: ConstellationDataCache | null = null): ConstellationDataStatus {
    if (!data) {
      return {
        hasData: false,
        isExpired: true,
        lastUpdated: null,
        nextUpdate: null,
        constellationCount: 0,
        satelliteCount: 0,
        dataAge: '无数据',
        isUpdating: this.isUpdating,
        lastError: this.lastError
      };
    }

    const now = new Date();
    const lastUpdated = new Date(data.lastUpdated);
    const nextUpdate = new Date(data.nextUpdate);
    const isExpired = now > nextUpdate;

    // 计算数据年龄
    const ageMs = now.getTime() - lastUpdated.getTime();
    const ageHours = Math.floor(ageMs / (1000 * 60 * 60));
    const ageMinutes = Math.floor((ageMs % (1000 * 60 * 60)) / (1000 * 60));
    
    let dataAge: string;
    if (ageHours > 0) {
      dataAge = `${ageHours}小时前`;
    } else if (ageMinutes > 0) {
      dataAge = `${ageMinutes}分钟前`;
    } else {
      dataAge = '刚刚';
    }

    return {
      hasData: true,
      isExpired,
      lastUpdated: data.lastUpdated,
      nextUpdate: data.nextUpdate,
      constellationCount: data.totalConstellations,
      satelliteCount: data.totalSatellites,
      dataAge,
      isUpdating: this.isUpdating,
      lastError: this.lastError
    };
  }

  /**
   * 手动刷新数据
   */
  async refreshData(): Promise<ConstellationDataCache | null> {
    console.log('🔄 手动刷新星座数据');
    return await this.updateConstellationData(true);
  }

  /**
   * 清除缓存数据
   */
  async clearCache(): Promise<void> {
    console.log('🗑️ 清除星座缓存数据');
    await simpleIndexedDB.deleteConstellationData();
    this.notifyStatusChange();
  }



  /**
   * 设置事件监听器
   */
  setListeners(listeners: Partial<typeof this.listeners>): void {
    this.listeners = { ...this.listeners, ...listeners };
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
      this.retryTimeout = null;
    }
    this.listeners = {};
    console.log('🔥 星座数据管理器已销毁');
  }

  // 通知方法
  private notifyUpdateStart(): void {
    this.listeners.onUpdateStart?.();
  }

  private notifyUpdateProgress(progress: number, message: string): void {
    this.listeners.onUpdateProgress?.(progress, message);
  }

  private notifyUpdateComplete(data: ConstellationDataCache): void {
    this.listeners.onUpdateComplete?.(data);
  }

  private notifyUpdateError(error: string): void {
    this.listeners.onUpdateError?.(error);
  }

  private notifyStatusChange(): void {
    const status = this.getDataStatus();
    this.listeners.onStatusChange?.(status);
  }
}

// 导出单例实例
export const constellationDataManager = new ConstellationDataManager(); 