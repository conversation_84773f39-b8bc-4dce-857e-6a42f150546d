export interface LaunchSite {
  id: string;
  name: string;
  country: string;
  city: string;
  cityCode: string;
  description: string;
  position: {
    longitude: number;  // 经度
    latitude: number;   // 纬度
    altitude: number;   // 海拔（米）
  };
} 

/**
 * 发射场Wiki信息接口
 */
export interface WikiLaunchSiteInfo {
  country: string;
  site_name: string[];
  info_source: string[];
  location: string;
  operational_date: string;
  launch_num: string;
  launch_heaviest: string;
  launch_highest: string;
  notes: string;
  data_import_time: string;
  city_code?: string;
  pad_name?: string;
  rocket_type?: string;
} 

/**
 * API返回的发射场数据接口
 */
export interface ApiLaunchSiteData {
  english_name: string;
  chinese_name: string;
  location: string; // 格式如："31.09951°N 2.83581°W"
}

/**
 * API响应接口
 */
export interface LaunchSiteApiResponse {
  success: boolean;
  data: ApiLaunchSiteData[];
}

/**
 * 解析经纬度字符串
 * @param location 经纬度字符串，格式如："31.09951°N 2.83581°W"
 * @returns 解析后的经纬度对象，如果解析失败返回null
 */
export function parseCoordinates(location: string): { latitude: number; longitude: number } | null {
  try {
    // 正则表达式匹配经纬度格式
    const regex = /(\d+\.?\d*)°([NS])\s+(\d+\.?\d*)°([EW])/;
    const match = location.match(regex);
    
    if (!match) {
      console.error('无法解析经纬度字符串:', location);
      return null;
    }
    
    const [, latValue, latDirection, lngValue, lngDirection] = match;
    
    // 转换为数值
    let latitude = parseFloat(latValue);
    let longitude = parseFloat(lngValue);
    
    // 根据方向调整正负号
    if (latDirection === 'S') {
      latitude = -latitude;
    }
    if (lngDirection === 'W') {
      longitude = -longitude;
    }
    
    return { latitude, longitude };
  } catch (error) {
    console.error('解析经纬度时发生错误:', error);
    return null;
  }
}

/**
 * 将API数据转换为LaunchSite格式
 * @param apiData API返回的发射场数据
 * @param index 索引，用于生成ID
 * @returns 转换后的LaunchSite对象，如果转换失败返回null
 */
export function convertApiDataToLaunchSite(apiData: ApiLaunchSiteData, index: number): LaunchSite | null {
  try {
    const coordinates = parseCoordinates(apiData.location);
    if (!coordinates) {
      return null;
    }
    
    // 优先使用中文名称，如果中文名称为空或有问题，则使用英文名称
    let displayName = '';
    if (apiData.chinese_name && apiData.chinese_name.trim() && !isGarbledName(apiData.chinese_name)) {
      displayName = apiData.chinese_name.trim();
    } else if (apiData.english_name && apiData.english_name.trim()) {
      displayName = apiData.english_name.trim();
    } else {
      displayName = `发射场 ${index + 1}`;
    }
    
    return {
      id: `api-launch-site-${index}`,
      name: displayName,
      country: '未知', // API数据中没有国家信息
      city: '未知', // API数据中没有城市信息
      cityCode: `LS${index}`,
      description: `${apiData.english_name || '未知'} - ${apiData.chinese_name || '未知'}`,
      position: {
        longitude: coordinates.longitude,
        latitude: coordinates.latitude,
        altitude: 0 // API数据中没有海拔信息，默认为0
      }
    };
  } catch (error) {
    console.error('转换API数据时发生错误:', error);
    return null;
  }
}

/**
 * 检查名称是否包含明显的乱码
 */
function isGarbledName(name: string): boolean {
  if (!name) return true;
  
  // 检查是否包含明显的字符混乱模式
  const garbledPatterns = [
    /RBeketchLauncher/i,
    /RBerearRuler/i,
    /Sketchlons/i,
    /Thinazwa/i,
    /Ketchlons/i,
    /[A-Z][a-z]*[A-Z][a-z]*[A-Z][a-z]*[A-Z]/, // 连续大小写混合
    /(.{3,})\1/, // 重复模式
    /[bcdfghjklmnpqrstvwxyz]{5,}/i // 连续辅音字母
  ];
  
  return garbledPatterns.some(pattern => pattern.test(name));
} 