/**
 * 简化的IndexedDB管理器
 * 专门用于TLE数据缓存，去除复杂的重试逻辑
 */

export interface TleDataItem {
  cospar_id: string;
  norad_id: number;
  epoch: string;
  time: string;
  tle_raw: string;
  satellite_name: string;
}

export interface TleDataCache {
  id: string;
  lastUpdated: string;
  nextUpdate: string;
  total: number;
  apiResponse: {
    success: boolean;
    total: number;
    executionTime: number;
    timeRangeStart: string;
    timeRangeEnd: number;
    timeRangeMinutes: number;
    method: string;
    queryStrategy: string;
    sampleMode: boolean;
  };
  data: TleDataItem[];
}

export interface ConstellationInfo {
  name: string;
  satelliteCount: number;
}

export interface ConstellationDataCache {
  id: string;
  lastUpdated: string;
  nextUpdate: string;
  constellations: ConstellationInfo[];
  satelliteMapping: Record<string, number[]>;
  totalConstellations: number;
  totalSatellites: number;
}

class SimpleIndexedDB {
  private dbName = 'SpaceDataDB';
  private version = 1;
  private storeName = 'tleData';
  private db: IDBDatabase | null = null;
  private isInitializing = false;

  /**
   * 初始化数据库连接
   */
  async init(): Promise<void> {
    // 如果已经连接，直接返回
    if (this.db) {
      return;
    }

    // 如果正在初始化，等待完成
    if (this.isInitializing) {
      return new Promise((resolve, reject) => {
        const checkInterval = setInterval(() => {
          if (!this.isInitializing) {
            clearInterval(checkInterval);
            if (this.db) {
              resolve();
            } else {
              reject(new Error('初始化失败'));
            }
          }
        }, 100);
      });
    }

    this.isInitializing = true;

    try {
      console.log('💾 开始简化 IndexedDB 初始化...');
      
      // 检查浏览器支持
      if (!window.indexedDB) {
        throw new Error('当前浏览器不支持 IndexedDB');
      }

      await this.openDatabase();
      console.log('💾 IndexedDB 初始化成功');
    } catch (error) {
      console.error('💾 IndexedDB 初始化失败:', error);
      throw error;
    } finally {
      this.isInitializing = false;
    }
  }

  /**
   * 打开数据库
   */
  private openDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      // 设置5秒超时
      const timeout = setTimeout(() => {
        console.error('💾 数据库打开超时');
        reject(new Error('数据库打开超时'));
      }, 5000);

      request.onerror = () => {
        clearTimeout(timeout);
        console.error('💾 数据库打开失败:', request.error);
        reject(request.error || new Error('数据库打开失败'));
      };

      request.onsuccess = () => {
        clearTimeout(timeout);
        this.db = request.result;
        console.log('💾 数据库连接成功');
        
        // 添加错误处理
        this.db.onerror = (event) => {
          console.error('💾 数据库运行时错误:', event);
        };
        
        resolve();
      };

      request.onupgradeneeded = (event) => {
        console.log('💾 数据库需要升级');
        const db = (event.target as IDBOpenDBRequest).result;
        
        if (!db.objectStoreNames.contains(this.storeName)) {
          console.log('💾 创建对象存储:', this.storeName);
          db.createObjectStore(this.storeName, { keyPath: 'id' });
        }
      };

      request.onblocked = () => {
        clearTimeout(timeout);
        console.warn('💾 数据库被阻塞，请关闭其他标签页');
        reject(new Error('数据库被阻塞'));
      };
    });
  }

  /**
   * 保存TLE数据
   */
  async saveTleData(data: TleDataCache): Promise<void> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      // 设置30秒超时
      const timeout = setTimeout(() => {
        console.error('💾 保存操作超时');
        transaction.abort();
        reject(new Error('保存操作超时'));
      }, 30000);

      transaction.oncomplete = () => {
        clearTimeout(timeout);
        console.log('💾 TLE数据保存成功');
        resolve();
      };

      transaction.onerror = () => {
        clearTimeout(timeout);
        console.error('💾 保存事务失败:', transaction.error);
        reject(transaction.error || new Error('保存失败'));
      };

      transaction.onabort = () => {
        clearTimeout(timeout);
        console.error('💾 保存事务被中止');
        reject(new Error('保存事务被中止'));
      };

      const request = store.put(data);
      
      request.onerror = () => {
        clearTimeout(timeout);
        console.error('💾 保存请求失败:', request.error);
        reject(request.error || new Error('保存请求失败'));
      };
    });
  }

  /**
   * 读取TLE数据
   */
  async getTleData(): Promise<TleDataCache | null> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      const request = store.get('tle-cache');

      request.onsuccess = () => {
        const result = request.result as TleDataCache | undefined;
        if (result && result.data && Array.isArray(result.data)) {
          console.log(`💾 读取到TLE缓存数据，共${result.total}颗卫星`);
          resolve(result);
        } else {
          console.log('💾 未找到有效的TLE缓存数据');
          resolve(null);
        }
      };

      request.onerror = () => {
        console.error('💾 读取TLE数据失败:', request.error);
        reject(request.error || new Error('读取失败'));
      };
    });
  }

  /**
   * 删除TLE数据
   */
  async deleteTleData(): Promise<void> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      const request = store.delete('tle-cache');

      request.onsuccess = () => {
        console.log('💾 TLE缓存数据已删除');
        resolve();
      };

      request.onerror = () => {
        console.error('💾 删除TLE数据失败:', request.error);
        reject(request.error || new Error('删除失败'));
      };
    });
  }

  /**
   * 获取数据状态
   */
  getDataStatus(cachedData: TleDataCache | null): {
    hasData: boolean;
    isExpired: boolean;
    lastUpdated: string | null;
    nextUpdate: string | null;
    satelliteCount: number;
    dataAge: string;
  } {
    if (!cachedData) {
      return {
        hasData: false,
        isExpired: true,
        lastUpdated: null,
        nextUpdate: null,
        satelliteCount: 0,
        dataAge: '无数据'
      };
    }

    const now = new Date();
    const lastUpdated = new Date(cachedData.lastUpdated);
    const nextUpdate = new Date(cachedData.nextUpdate);
    const isExpired = now > nextUpdate;
    
    // 计算数据年龄
    const ageMs = now.getTime() - lastUpdated.getTime();
    const ageHours = Math.floor(ageMs / (1000 * 60 * 60));
    const ageMinutes = Math.floor((ageMs % (1000 * 60 * 60)) / (1000 * 60));
    
    let dataAge: string;
    if (ageHours > 0) {
      dataAge = `${ageHours}小时${ageMinutes}分钟前`;
    } else {
      dataAge = `${ageMinutes}分钟前`;
    }

    return {
      hasData: true,
      isExpired,
      lastUpdated: cachedData.lastUpdated,
      nextUpdate: cachedData.nextUpdate,
      satelliteCount: cachedData.total,
      dataAge
    };
  }

  /**
   * 保存星座数据
   */
  async saveConstellationData(data: ConstellationDataCache): Promise<void> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);

      // 设置30秒超时
      const timeout = setTimeout(() => {
        console.error('💾 保存星座数据超时');
        transaction.abort();
        reject(new Error('保存星座数据超时'));
      }, 30000);

      transaction.oncomplete = () => {
        clearTimeout(timeout);
        console.log('💾 星座数据保存成功');
        resolve();
      };

      transaction.onerror = () => {
        clearTimeout(timeout);
        console.error('💾 保存星座数据事务失败:', transaction.error);
        reject(transaction.error || new Error('保存星座数据失败'));
      };

      transaction.onabort = () => {
        clearTimeout(timeout);
        console.error('💾 保存星座数据事务被中止');
        reject(new Error('保存星座数据事务被中止'));
      };

      const request = store.put(data);

      request.onerror = () => {
        clearTimeout(timeout);
        console.error('💾 保存星座数据请求失败:', request.error);
        reject(request.error || new Error('保存星座数据请求失败'));
      };
    });
  }

  /**
   * 读取星座数据
   */
  async getConstellationData(): Promise<ConstellationDataCache | null> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);

      const request = store.get('constellation-cache');

      request.onsuccess = () => {
        const result = request.result as ConstellationDataCache | undefined;
        if (result && result.constellations && Array.isArray(result.constellations)) {
          console.log(`💾 读取到星座缓存数据，共${result.totalConstellations}个星座`);
          resolve(result);
        } else {
          console.log('💾 未找到有效的星座缓存数据');
          resolve(null);
        }
      };

      request.onerror = () => {
        console.error('💾 读取星座数据失败:', request.error);
        reject(request.error || new Error('读取星座数据失败'));
      };
    });
  }

  /**
   * 删除星座数据
   */
  async deleteConstellationData(): Promise<void> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);

      const request = store.delete('constellation-cache');

      request.onsuccess = () => {
        console.log('💾 星座缓存数据已删除');
        resolve();
      };

      request.onerror = () => {
        console.error('💾 删除星座数据失败:', request.error);
        reject(request.error || new Error('删除星座数据失败'));
      };
    });
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      console.log('💾 数据库连接已关闭');
    }
  }
}

// 导出单例实例
export const simpleIndexedDB = new SimpleIndexedDB();
